# How to Run Standalone Accounting Software

## 🚀 Super Simple Start (Any Operating System)

1. **Locate** the `index.html` file
2. **Double-click** the file to open in your browser
3. **Login** with: `admin` / `admin`

That's it! No installation, no setup, no server required!

## 📂 Different Ways to Open the Application

### Method 1: Double-Click (Easiest)
- **What to do**: Double-click the `index.html` file
- **Best for**: Most users
- **Result**: Opens in your default browser automatically

### Method 2: Browser Menu
- **What to do**: Open browser → File → Open → Select `index.html`
- **Best for**: When double-click doesn't work
- **Keyboard shortcut**: `Ctrl+O` (Windows) or `Cmd+O` (Mac)

### Method 3: Drag and Drop
- **What to do**: Drag `index.html` into an open browser window
- **Best for**: Quick access when browser is already open

### Method 4: Right-Click Menu
- **What to do**: Right-click `index.html` → "Open with" → Choose browser
- **Best for**: Selecting a specific browser

## 🌐 No URLs Needed!

Since this is a standalone file, you don't need to remember any URLs:
- **File Location**: Wherever you saved `index.html`
- **Browser Address**: Will show `file:///path/to/index.html`
- **Access**: Direct file access, no server required

## ⚠️ Troubleshooting

### Problem: File won't open
**Solutions**:
1. Ensure you have a web browser installed
2. Try right-clicking → "Open with" → Choose browser
3. Check that the file isn't corrupted

### Problem: Login not working
**Solutions**:
1. Use exactly: `admin` / `admin` (case-sensitive)
2. Clear browser cache (Ctrl+F5)
3. Try a different browser
4. Ensure JavaScript is enabled

### Problem: Blank page or errors
**Solutions**:
1. Check browser console (F12) for error messages
2. Update to latest browser version
3. Disable browser extensions temporarily
4. Try incognito/private browsing mode

### Problem: Data not saving
**Solutions**:
1. Don't use private/incognito browsing mode
2. Ensure browser allows local storage
3. Check available storage space
4. Try a different browser

## 💡 Tips for Best Experience

1. **Use modern browsers**: Chrome, Firefox, Safari, or Edge
2. **Enable JavaScript**: Required for all functionality
3. **Allow local storage**: Needed for data persistence
4. **Regular backups**: Export data regularly using Reports section
5. **Update browser**: Keep browser updated for security and performance

## 🔄 No Need to Stop Anything!

Since this is a standalone file:
- **No server to stop**
- **No processes to kill**
- **Just close the browser tab when done**
- **Data is automatically saved**

## 📞 Getting Help

If you're having issues:
1. **Check browser console** (F12) for error messages
2. **Try different browser** (Chrome, Firefox, Safari, Edge)
3. **Clear browser cache** (Ctrl+F5 or Cmd+Shift+R)
4. **Review documentation** files included

## 🎯 Default Credentials

- **Username**: `admin`
- **Password**: `admin`

**Important**: Change the password after first login using Settings!

## 🌟 Advantages of Standalone Version

- ✅ **No installation required**
- ✅ **No server setup needed**
- ✅ **No database configuration**
- ✅ **Works offline**
- ✅ **Portable - runs from USB drive**
- ✅ **No maintenance required**
- ✅ **Instant startup**
- ✅ **Complete data privacy**

---

**Enjoy your professional accounting software - no technical complexity required!**
