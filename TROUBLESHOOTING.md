# Troubleshooting Guide

## Error: "Failed to execute 'json' on 'Response': Unexpected end of JSON input"

This error occurs when the frontend tries to access API endpoints but the Next.js server is not running.

### Quick Solutions:

#### Option 1: Use Standalone Demo (Recommended for Testing)
1. Open `standalone-demo.html` in your browser
2. Login with: `admin` / `admin`
3. Explore the interface in demo mode
4. No server setup required!

#### Option 2: Run Full Application with Server

**Step 1: Install Node.js**
- Download from: https://nodejs.org/
- Install the LTS version
- Restart your computer after installation

**Step 2: Start the Server**
```bash
# Run the quick start script
quick-start.bat

# OR manually:
npm install
npm run dev
```

**Step 3: Access the Application**
- Open: http://localhost:3000
- Login with: `admin` / `admin`

### Common Issues and Solutions:

#### 1. "Node.js is not installed"
**Solution:** Install Node.js from https://nodejs.org/

#### 2. "npm is not recognized"
**Solution:** 
- Restart your command prompt/terminal
- Add Node.js to your PATH environment variable
- Reinstall Node.js

#### 3. "Port 3000 is already in use"
**Solution:**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use a different port
npm run dev -- -p 3001
```

#### 4. "Module not found" errors
**Solution:**
```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules
npm install
```

#### 5. Database connection errors
**Solution:**
- Make sure MySQL is installed and running
- Run the database setup: `setup-database.bat`
- Check your database credentials in `.env.local`

#### 6. "Permission denied" on Windows
**Solution:**
```powershell
# Run as Administrator and enable script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### File Structure Check:

Make sure you have these files:
```
├── package.json
├── next.config.js
├── pages/api/auth/login.js
├── lib/db.js
├── lib/auth.js
├── frontend/index.html
└── database/schema.sql
```

### Environment Setup:

Create `.env.local` file:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=enterprise_accounting
JWT_SECRET=your-secret-key
ENCRYPTION_KEY=your-encryption-key
```

### Testing the Setup:

1. **Test Node.js:** `node --version`
2. **Test npm:** `npm --version`
3. **Test MySQL:** `mysql --version`
4. **Test Server:** `npm run dev`

### Alternative: Docker Setup (Advanced)

If you have Docker installed:
```bash
# Build and run with Docker
docker-compose up -d
```

### Getting Help:

If you're still having issues:

1. **Check the console:** Open browser developer tools (F12) and check for errors
2. **Check server logs:** Look at the terminal where you ran `npm run dev`
3. **Verify database:** Make sure MySQL is running and accessible
4. **Check ports:** Ensure port 3000 is not blocked by firewall

### Quick Demo Mode:

For immediate testing without server setup:
1. Open `standalone-demo.html`
2. No installation required
3. Works offline
4. Demonstrates all UI features

### Production Deployment:

For production deployment:
```bash
npm run build
npm start
```

### System Requirements:

- **Node.js:** 18.0 or higher
- **MySQL:** 8.0 or higher
- **RAM:** 4GB minimum
- **Storage:** 1GB free space
- **OS:** Windows 10+, macOS 10.15+, or Linux

### Performance Tips:

1. **Close unnecessary applications** while running the development server
2. **Use SSD storage** for better performance
3. **Increase Node.js memory** if needed: `node --max-old-space-size=4096`
4. **Use production build** for better performance: `npm run build && npm start`

### Security Notes:

- Change default passwords immediately
- Use strong JWT secrets in production
- Enable HTTPS in production
- Regular security updates

---

**Still having issues?** 
- Try the standalone demo first: `standalone-demo.html`
- Check if all prerequisites are installed
- Verify your internet connection for npm packages
- Run as administrator if permission issues occur
