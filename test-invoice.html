<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Invoice System Test</h1>
    
    <div id="alertContainer"></div>
    
    <h2>Create Invoice</h2>
    <form id="createInvoiceForm">
        <div class="form-group">
            <label for="customerId">Customer</label>
            <select id="customerId" name="customerId" required>
                <option value="">Select Customer</option>
                <option value="1">ABC Corporation</option>
                <option value="2">XYZ Ltd</option>
                <option value="3">Tech Solutions Inc</option>
            </select>
        </div>
        <div class="form-group">
            <label for="amount">Amount</label>
            <input type="number" id="amount" name="amount" step="0.01" required>
        </div>
        <div class="form-group">
            <label for="invoiceDate">Invoice Date</label>
            <input type="date" id="invoiceDate" name="invoiceDate" required>
        </div>
        <div class="form-group">
            <label for="dueDate">Due Date</label>
            <input type="date" id="dueDate" name="dueDate" required>
        </div>
        <div class="form-group">
            <label for="notes">Notes</label>
            <textarea id="notes" name="notes" rows="3"></textarea>
        </div>
        <button type="submit">Create Invoice</button>
    </form>
    
    <h2>Invoices</h2>
    <table>
        <thead>
            <tr>
                <th>Invoice #</th>
                <th>Customer</th>
                <th>Amount</th>
                <th>Date</th>
                <th>Due Date</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="invoicesTableBody">
        </tbody>
    </table>
    
    <h2>Customers</h2>
    <table>
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Address</th>
                <th>Invoices</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="customersTableBody">
        </tbody>
    </table>

    <script src="JS/Invoices.js"></script>
    <script>
        // Override showAlert for testing
        window.invoicesManager.showAlert = function(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${type}`;
            alertDiv.textContent = message;
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
            
            console.log(`${type.toUpperCase()}: ${message}`);
        };
        
        // Override hideModal for testing
        window.invoicesManager.hideModal = function(modalId) {
            console.log(`Modal ${modalId} would be hidden`);
        };
        
        // Initialize the invoice manager
        window.invoicesManager.init();
        
        // Set up form handler
        document.getElementById('createInvoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            window.invoicesManager.handleCreateInvoice(e);
        });
        
        // Set default dates
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('invoiceDate').value = today;
        
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
    </script>
</body>
</html>
