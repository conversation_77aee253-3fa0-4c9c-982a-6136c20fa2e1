/**
 * Payroll.js - Comprehensive Payroll Management System
 * Manages payroll calculation, payslips, reports, employees, tax settings, and payroll history
 * Handles: Employee, Position, Basic Salary, Allowances, Overtime, Gross Pay, Tax, Deductions, Net Pay, Status, Actions
 */

class PayrollManager {
    constructor() {
        this.employees = [];
        this.payrollRecords = [];
        this.taxSettings = {};
        this.payrollHistory = [];
        this.currentPayPeriod = null;
        this.isInitialized = false;
        this.defaultTaxRates = {
            incomeTax: 0.20,        // 20%
            socialSecurity: 0.062,   // 6.2%
            medicare: 0.0145,        // 1.45%
            stateIncomeTax: 0.05     // 5%
        };
    }

    /**
     * Initialize the payroll management system
     */
    init() {
        if (this.isInitialized) return;
        
        this.loadData();
        this.setupEventListeners();
        this.loadPayrollTable();
        this.updatePayrollMetrics();
        this.setupRealTimeUpdates();
        this.initializeCurrentPayPeriod();
        
        this.isInitialized = true;
        console.log('Payroll Manager initialized successfully');
    }

    /**
     * Load data from localStorage or initialize with sample data
     */
    loadData() {
        // Load employees
        const savedEmployees = localStorage.getItem('accounting_employees');
        if (savedEmployees) {
            this.employees = JSON.parse(savedEmployees);
        } else {
            this.initializeSampleEmployees();
        }

        // Load payroll records
        const savedPayroll = localStorage.getItem('accounting_payroll_records');
        if (savedPayroll) {
            this.payrollRecords = JSON.parse(savedPayroll);
        } else {
            this.initializeSamplePayroll();
        }

        // Load tax settings
        const savedTaxSettings = localStorage.getItem('accounting_tax_settings');
        if (savedTaxSettings) {
            this.taxSettings = JSON.parse(savedTaxSettings);
        } else {
            this.taxSettings = { ...this.defaultTaxRates };
            this.saveTaxSettings();
        }

        // Load payroll history
        const savedHistory = localStorage.getItem('accounting_payroll_history');
        if (savedHistory) {
            this.payrollHistory = JSON.parse(savedHistory);
        }
    }

    /**
     * Initialize sample employees
     */
    initializeSampleEmployees() {
        this.employees = [
            {
                id: 1,
                firstName: 'John',
                lastName: 'Smith',
                email: '<EMAIL>',
                phone: '******-0201',
                position: 'Senior Accountant',
                department: 'accounting',
                basicSalary: 5000,
                allowances: 500,
                hireDate: '2023-01-15',
                isActive: true,
                bankAccount: '**********',
                taxId: 'TAX001',
                emergencyContact: 'Jane Smith - ******-0202',
                createdAt: new Date('2023-01-15'),
                updatedAt: new Date('2023-01-15')
            },
            {
                id: 2,
                firstName: 'Jane',
                lastName: 'Doe',
                email: '<EMAIL>',
                phone: '******-0203',
                position: 'Financial Analyst',
                department: 'accounting',
                basicSalary: 4500,
                allowances: 400,
                hireDate: '2023-03-01',
                isActive: true,
                bankAccount: '**********',
                taxId: 'TAX002',
                emergencyContact: 'John Doe - ******-0204',
                createdAt: new Date('2023-03-01'),
                updatedAt: new Date('2023-03-01')
            },
            {
                id: 3,
                firstName: 'Mike',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phone: '******-0205',
                position: 'Accounts Manager',
                department: 'accounting',
                basicSalary: 6000,
                allowances: 600,
                hireDate: '2022-08-15',
                isActive: true,
                bankAccount: '**********',
                taxId: 'TAX003',
                emergencyContact: 'Sarah Johnson - ******-0206',
                createdAt: new Date('2022-08-15'),
                updatedAt: new Date('2022-08-15')
            },
            {
                id: 4,
                firstName: 'Sarah',
                lastName: 'Wilson',
                email: '<EMAIL>',
                phone: '******-0207',
                position: 'Junior Accountant',
                department: 'accounting',
                basicSalary: 3500,
                allowances: 300,
                hireDate: '2024-01-10',
                isActive: true,
                bankAccount: '**********',
                taxId: 'TAX004',
                emergencyContact: 'Tom Wilson - ******-0208',
                createdAt: new Date('2024-01-10'),
                updatedAt: new Date('2024-01-10')
            },
            {
                id: 5,
                firstName: 'David',
                lastName: 'Brown',
                email: '<EMAIL>',
                phone: '******-0209',
                position: 'Payroll Specialist',
                department: 'hr',
                basicSalary: 4000,
                allowances: 350,
                hireDate: '2023-06-01',
                isActive: true,
                bankAccount: '**********',
                taxId: 'TAX005',
                emergencyContact: 'Lisa Brown - ******-0210',
                createdAt: new Date('2023-06-01'),
                updatedAt: new Date('2023-06-01')
            }
        ];
        this.saveEmployees();
    }

    /**
     * Initialize sample payroll records
     */
    initializeSamplePayroll() {
        this.payrollRecords = this.employees.map(employee => {
            const overtime = Math.floor(Math.random() * 20) * 25; // Random overtime hours * $25/hour
            const grossPay = employee.basicSalary + employee.allowances + overtime;
            const totalTax = this.calculateTotalTax(grossPay);
            const deductions = Math.floor(Math.random() * 200) + 50; // Random deductions
            const netPay = grossPay - totalTax - deductions;

            return {
                id: Date.now() + employee.id,
                employeeId: employee.id,
                payPeriod: this.getCurrentPayPeriod(),
                basicSalary: employee.basicSalary,
                allowances: employee.allowances,
                overtime: overtime,
                grossPay: grossPay,
                incomeTax: grossPay * this.taxSettings.incomeTax,
                socialSecurity: grossPay * this.taxSettings.socialSecurity,
                medicare: grossPay * this.taxSettings.medicare,
                stateIncomeTax: grossPay * this.taxSettings.stateIncomeTax,
                totalTax: totalTax,
                deductions: deductions,
                netPay: netPay,
                status: 'pending',
                createdAt: new Date(),
                updatedAt: new Date()
            };
        });
        this.savePayrollRecords();
    }

    /**
     * Save data to localStorage
     */
    saveEmployees() {
        localStorage.setItem('accounting_employees', JSON.stringify(this.employees));
        this.updatePayrollMetrics();
        this.notifyDashboard();
    }

    /**
     * Save payroll records
     */
    savePayrollRecords() {
        localStorage.setItem('accounting_payroll_records', JSON.stringify(this.payrollRecords));
        this.updatePayrollMetrics();
        this.notifyDashboard();
    }

    /**
     * Save tax settings
     */
    saveTaxSettings() {
        localStorage.setItem('accounting_tax_settings', JSON.stringify(this.taxSettings));
    }

    /**
     * Save payroll history
     */
    savePayrollHistory() {
        localStorage.setItem('accounting_payroll_history', JSON.stringify(this.payrollHistory));
    }

    /**
     * Create new employee
     */
    createEmployee(employeeData) {
        const newEmployee = {
            id: Date.now(),
            firstName: employeeData.firstName,
            lastName: employeeData.lastName,
            email: employeeData.email,
            phone: employeeData.phone || '',
            position: employeeData.position,
            department: employeeData.department,
            basicSalary: parseFloat(employeeData.basicSalary),
            allowances: parseFloat(employeeData.allowances || 0),
            hireDate: employeeData.hireDate,
            isActive: true,
            bankAccount: employeeData.bankAccount || '',
            taxId: employeeData.taxId || '',
            emergencyContact: employeeData.emergencyContact || '',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.employees.push(newEmployee);
        this.saveEmployees();
        this.loadPayrollTable();
        
        return newEmployee;
    }

    /**
     * Update existing employee
     */
    updateEmployee(employeeId, updateData) {
        const index = this.employees.findIndex(emp => emp.id === employeeId);
        if (index === -1) return false;

        const employee = this.employees[index];
        
        // Update fields
        Object.keys(updateData).forEach(key => {
            if (key !== 'id' && key !== 'createdAt') {
                employee[key] = updateData[key];
            }
        });

        employee.updatedAt = new Date();
        
        this.saveEmployees();
        this.loadPayrollTable();
        
        return employee;
    }

    /**
     * Deactivate employee
     */
    deactivateEmployee(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        if (!employee) return false;

        employee.isActive = false;
        employee.terminationDate = new Date();
        employee.updatedAt = new Date();
        
        this.saveEmployees();
        this.loadPayrollTable();
        
        return true;
    }

    /**
     * Calculate payroll for all employees
     */
    calculatePayroll(payPeriodData = {}) {
        const payPeriod = payPeriodData.payPeriod || this.getCurrentPayPeriod();
        const payrollType = payPeriodData.payrollType || 'regular';
        
        this.payrollRecords = this.employees
            .filter(emp => emp.isActive)
            .map(employee => {
                const overtime = this.calculateOvertime(employee.id, payPeriod);
                const bonuses = this.calculateBonuses(employee.id, payrollType);
                const grossPay = employee.basicSalary + employee.allowances + overtime + bonuses;
                const totalTax = this.calculateTotalTax(grossPay);
                const deductions = this.calculateDeductions(employee.id);
                const netPay = grossPay - totalTax - deductions;

                return {
                    id: Date.now() + employee.id,
                    employeeId: employee.id,
                    payPeriod: payPeriod,
                    payrollType: payrollType,
                    basicSalary: employee.basicSalary,
                    allowances: employee.allowances,
                    overtime: overtime,
                    bonuses: bonuses,
                    grossPay: grossPay,
                    incomeTax: grossPay * this.taxSettings.incomeTax,
                    socialSecurity: grossPay * this.taxSettings.socialSecurity,
                    medicare: grossPay * this.taxSettings.medicare,
                    stateIncomeTax: grossPay * this.taxSettings.stateIncomeTax,
                    totalTax: totalTax,
                    deductions: deductions,
                    netPay: netPay,
                    status: 'calculated',
                    calculatedAt: new Date(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
            });

        this.savePayrollRecords();
        this.loadPayrollTable();
        
        return this.payrollRecords;
    }

    /**
     * Calculate total tax for an employee
     */
    calculateTotalTax(grossPay) {
        const incomeTax = grossPay * this.taxSettings.incomeTax;
        const socialSecurity = grossPay * this.taxSettings.socialSecurity;
        const medicare = grossPay * this.taxSettings.medicare;
        const stateIncomeTax = grossPay * this.taxSettings.stateIncomeTax;
        
        return incomeTax + socialSecurity + medicare + stateIncomeTax;
    }

    /**
     * Calculate overtime for an employee
     */
    calculateOvertime(employeeId, payPeriod) {
        // This would typically come from time tracking system
        // For demo, return random overtime
        return Math.floor(Math.random() * 20) * 37.5; // Random hours * overtime rate
    }

    /**
     * Calculate bonuses for an employee
     */
    calculateBonuses(employeeId, payrollType) {
        if (payrollType === 'bonus') {
            const employee = this.employees.find(emp => emp.id === employeeId);
            return employee ? employee.basicSalary * 0.1 : 0; // 10% bonus
        }
        return 0;
    }

    /**
     * Calculate deductions for an employee
     */
    calculateDeductions(employeeId) {
        // This would include health insurance, retirement contributions, etc.
        // For demo, return random deductions
        return Math.floor(Math.random() * 200) + 100;
    }

    /**
     * Process payroll (approve all calculated payroll)
     */
    processPayroll() {
        const processedCount = this.payrollRecords.filter(record => {
            if (record.status === 'calculated' || record.status === 'pending') {
                record.status = 'processed';
                record.processedAt = new Date();
                record.updatedAt = new Date();
                return true;
            }
            return false;
        }).length;

        if (processedCount > 0) {
            // Add to payroll history
            this.payrollHistory.push({
                id: Date.now(),
                payPeriod: this.getCurrentPayPeriod(),
                processedAt: new Date(),
                employeeCount: processedCount,
                totalGrossPay: this.payrollRecords.reduce((sum, record) => sum + record.grossPay, 0),
                totalNetPay: this.payrollRecords.reduce((sum, record) => sum + record.netPay, 0),
                totalTax: this.payrollRecords.reduce((sum, record) => sum + record.totalTax, 0),
                records: [...this.payrollRecords]
            });

            this.savePayrollRecords();
            this.savePayrollHistory();
            this.loadPayrollTable();
        }

        return processedCount;
    }

    /**
     * Generate payslips for all employees
     */
    generatePayslips() {
        const payslips = this.payrollRecords
            .filter(record => record.status === 'processed')
            .map(record => {
                const employee = this.employees.find(emp => emp.id === record.employeeId);
                return this.generatePayslip(employee, record);
            });

        return payslips;
    }

    /**
     * Generate individual payslip
     */
    generatePayslip(employee, payrollRecord) {
        return {
            payslipId: `PS-${payrollRecord.id}`,
            employee: employee,
            payPeriod: payrollRecord.payPeriod,
            basicSalary: payrollRecord.basicSalary,
            allowances: payrollRecord.allowances,
            overtime: payrollRecord.overtime,
            bonuses: payrollRecord.bonuses || 0,
            grossPay: payrollRecord.grossPay,
            taxBreakdown: {
                incomeTax: payrollRecord.incomeTax,
                socialSecurity: payrollRecord.socialSecurity,
                medicare: payrollRecord.medicare,
                stateIncomeTax: payrollRecord.stateIncomeTax
            },
            totalTax: payrollRecord.totalTax,
            deductions: payrollRecord.deductions,
            netPay: payrollRecord.netPay,
            generatedAt: new Date()
        };
    }

    /**
     * Load and display payroll table
     */
    loadPayrollTable() {
        const tbody = document.getElementById('payrollTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        if (this.payrollRecords.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center">No payroll records found</td>
                </tr>
            `;
            return;
        }

        this.payrollRecords.forEach(record => {
            const employee = this.employees.find(emp => emp.id === record.employeeId);
            if (!employee) return;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${employee.firstName} ${employee.lastName}</td>
                <td>${employee.position}</td>
                <td>$${record.basicSalary.toLocaleString()}</td>
                <td>$${record.allowances.toLocaleString()}</td>
                <td>$${record.overtime.toLocaleString()}</td>
                <td>$${record.grossPay.toLocaleString()}</td>
                <td>$${record.totalTax.toLocaleString()}</td>
                <td>$${record.deductions.toLocaleString()}</td>
                <td>$${record.netPay.toLocaleString()}</td>
                <td><span class="status-badge ${record.status}">${record.status}</span></td>
                <td>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-secondary" onclick="payrollManager.viewPayrollDetails(${record.id})" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="payrollManager.editPayroll(${record.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${record.status === 'calculated' ? `
                            <button class="btn btn-sm btn-success" onclick="payrollManager.approvePayroll(${record.id})" title="Approve">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        ${record.status === 'processed' ? `
                            <button class="btn btn-sm btn-info" onclick="payrollManager.generatePayslip(${employee.id}, ${record.id})" title="Generate Payslip">
                                <i class="fas fa-file-pdf"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * Update payroll metrics
     */
    updatePayrollMetrics() {
        const activeEmployees = this.employees.filter(emp => emp.isActive).length;
        const totalGrossPay = this.payrollRecords.reduce((sum, record) => sum + record.grossPay, 0);
        const totalNetPay = this.payrollRecords.reduce((sum, record) => sum + record.netPay, 0);
        const totalTax = this.payrollRecords.reduce((sum, record) => sum + record.totalTax, 0);
        const processedRecords = this.payrollRecords.filter(record => record.status === 'processed').length;

        // Update metric cards
        this.updateMetricCard('activeEmployees', activeEmployees);
        this.updateMetricCard('totalGrossPay', `$${totalGrossPay.toLocaleString()}`);
        this.updateMetricCard('totalNetPay', `$${totalNetPay.toLocaleString()}`);
        this.updateMetricCard('totalTaxDeductions', `$${totalTax.toLocaleString()}`);
        this.updateMetricCard('processedPayrolls', processedRecords);
    }

    /**
     * Update metric card
     */
    updateMetricCard(metricId, value) {
        const element = document.getElementById(metricId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Calculate payroll button
        const calculateBtn = document.getElementById('calculatePayrollBtn');
        if (calculateBtn) {
            calculateBtn.addEventListener('click', () => {
                this.calculatePayrollForPeriod();
            });
        }

        // Process payroll button
        const processBtn = document.getElementById('processPayrollBtn');
        if (processBtn) {
            processBtn.addEventListener('click', () => {
                this.processPayrollConfirm();
            });
        }

        // Generate payslips button
        const payslipsBtn = document.getElementById('generatePayslipsBtn');
        if (payslipsBtn) {
            payslipsBtn.addEventListener('click', () => {
                this.generateAllPayslips();
            });
        }

        // Employee form
        const employeeForm = document.getElementById('createEmployeeForm');
        if (employeeForm) {
            employeeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateEmployee(e);
            });
        }

        // Tax settings form
        const taxForm = document.getElementById('taxSettingsForm');
        if (taxForm) {
            taxForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleUpdateTaxSettings(e);
            });
        }
    }

    /**
     * Initialize current pay period
     */
    initializeCurrentPayPeriod() {
        this.currentPayPeriod = this.getCurrentPayPeriod();
    }

    /**
     * Get current pay period
     */
    getCurrentPayPeriod() {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1;
        return `${year}-${String(month).padStart(2, '0')}`;
    }

    /**
     * Setup real-time updates
     */
    setupRealTimeUpdates() {
        // Auto-save every 30 seconds
        setInterval(() => {
            this.saveEmployees();
            this.savePayrollRecords();
        }, 30000);
    }

    /**
     * Calculate payroll for current period
     */
    calculatePayrollForPeriod() {
        const payPeriodData = {
            payPeriod: this.getCurrentPayPeriod(),
            payrollType: 'regular'
        };

        const records = this.calculatePayroll(payPeriodData);
        this.showAlert(`Payroll calculated for ${records.length} employees!`, 'success');
    }

    /**
     * Process payroll with confirmation
     */
    processPayrollConfirm() {
        const pendingCount = this.payrollRecords.filter(r => r.status === 'calculated').length;
        
        if (pendingCount === 0) {
            this.showAlert('No calculated payroll records to process.', 'warning');
            return;
        }

        if (confirm(`Are you sure you want to process payroll for ${pendingCount} employees? This action cannot be undone.`)) {
            const processedCount = this.processPayroll();
            this.showAlert(`Payroll processed successfully for ${processedCount} employees!`, 'success');
        }
    }

    /**
     * Generate all payslips
     */
    generateAllPayslips() {
        const payslips = this.generatePayslips();
        
        if (payslips.length === 0) {
            this.showAlert('No processed payroll records found to generate payslips.', 'warning');
            return;
        }

        // In a real application, this would generate PDF files
        this.showAlert(`${payslips.length} payslips generated successfully!`, 'success');
        console.log('Generated payslips:', payslips);
    }

    /**
     * Handle create employee form
     */
    handleCreateEmployee(event) {
        const formData = new FormData(event.target);
        const employeeData = Object.fromEntries(formData);

        // Validation
        if (!employeeData.firstName || !employeeData.lastName || !employeeData.email || !employeeData.position || !employeeData.basicSalary) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        if (parseFloat(employeeData.basicSalary) <= 0) {
            this.showAlert('Basic salary must be greater than zero.', 'error');
            return;
        }

        const newEmployee = this.createEmployee(employeeData);
        this.showAlert(`Employee ${newEmployee.firstName} ${newEmployee.lastName} added successfully!`, 'success');
        
        // Reset form and close modal
        event.target.reset();
        this.hideModal('employeeManagementModal');
    }

    /**
     * Handle update tax settings
     */
    handleUpdateTaxSettings(event) {
        const formData = new FormData(event.target);
        const taxData = Object.fromEntries(formData);

        // Update tax settings
        this.taxSettings.incomeTax = parseFloat(taxData.incomeTax) / 100;
        this.taxSettings.socialSecurity = parseFloat(taxData.socialSecurity) / 100;
        this.taxSettings.medicare = parseFloat(taxData.medicare) / 100;
        this.taxSettings.stateIncomeTax = parseFloat(taxData.stateIncomeTax) / 100;

        this.saveTaxSettings();
        this.showAlert('Tax settings updated successfully!', 'success');
        
        // Close modal
        this.hideModal('taxSettingsModal');
    }

    /**
     * View payroll details
     */
    viewPayrollDetails(recordId) {
        const record = this.payrollRecords.find(r => r.id === recordId);
        if (!record) return;

        console.log('Viewing payroll details:', record);
        this.showAlert('Payroll details view would open here.', 'info');
    }

    /**
     * Edit payroll record
     */
    editPayroll(recordId) {
        const record = this.payrollRecords.find(r => r.id === recordId);
        if (!record) return;

        console.log('Editing payroll:', record);
        this.showAlert('Payroll edit form would open here.', 'info');
    }

    /**
     * Approve individual payroll record
     */
    approvePayroll(recordId) {
        const record = this.payrollRecords.find(r => r.id === recordId);
        if (!record) return;

        record.status = 'approved';
        record.approvedAt = new Date();
        record.updatedAt = new Date();

        this.savePayrollRecords();
        this.loadPayrollTable();
        this.showAlert('Payroll record approved successfully!', 'success');
    }

    /**
     * Get all employees (for dashboard integration)
     */
    getAllEmployees() {
        return this.employees;
    }

    /**
     * Show alert message
     */
    showAlert(message, type) {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        if (window.showAlert) {
            window.showAlert(message, type);
        }
    }

    /**
     * Hide modal
     */
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Notify dashboard of changes
     */
    notifyDashboard() {
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboardMetrics();
        }
    }
}

// Create global instance
window.payrollManager = new PayrollManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.payrollManager.init();
    });
} else {
    window.payrollManager.init();
}
