<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Accounting Software - Complete Application</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* CSS Variables for theming */
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #06b6d4;
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-5: 1.25rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 300ms ease-in-out;
        }

        /* Dark theme */
        [data-theme="dark"] {
            --white: #0f172a;
            --gray-50: #1e293b;
            --gray-100: #334155;
            --gray-200: #475569;
            --gray-300: #64748b;
            --gray-400: #94a3b8;
            --gray-500: #cbd5e1;
            --gray-600: #e2e8f0;
            --gray-700: #f1f5f9;
            --gray-800: #f8fafc;
            --gray-900: #ffffff;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family);
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-900);
            background-color: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.25;
            margin-bottom: var(--spacing-4);
        }

        h1 { font-size: 1.875rem; }
        h2 { font-size: 1.5rem; }
        h3 { font-size: 1.25rem; }
        h4 { font-size: 1.125rem; }

        p {
            margin-bottom: var(--spacing-4);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-2);
            padding: var(--spacing-3) var(--spacing-6);
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-fast);
            min-height: 44px;
            width: 100%;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background-color: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-200);
        }

        .btn-secondary:hover {
            background-color: var(--gray-200);
            color: var(--gray-800);
        }

        .btn-success {
            background-color: var(--success-color);
            color: var(--white);
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: var(--white);
        }

        .btn-danger {
            background-color: var(--error-color);
            color: var(--white);
        }

        .btn-sm {
            padding: var(--spacing-1) var(--spacing-3);
            font-size: 0.875rem;
            min-height: auto;
            width: auto;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: var(--spacing-5);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-4);
        }

        label {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: var(--spacing-2);
            font-size: 0.875rem;
        }

        input, select, textarea {
            width: 100%;
            padding: var(--spacing-3) var(--spacing-4);
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
            background-color: var(--white);
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        input::placeholder {
            color: var(--gray-400);
        }

        /* Login Page Styles */
        .login-page {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-4);
        }

        .login-container {
            width: 100%;
            max-width: 450px;
        }

        .login-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-8);
            margin-bottom: var(--spacing-6);
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--spacing-8);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-4);
        }

        .logo i {
            font-size: 1.875rem;
            color: var(--primary-color);
        }

        .logo h1 {
            color: var(--gray-900);
            margin: 0;
        }

        .subtitle {
            color: var(--gray-600);
            font-size: 0.875rem;
            margin: 0;
        }

        .password-input {
            position: relative;
        }

        .toggle-password {
            position: absolute;
            right: var(--spacing-3);
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            padding: var(--spacing-2);
            transition: color var(--transition-fast);
        }

        .toggle-password:hover {
            color: var(--gray-600);
        }

        /* Dashboard Layout */
        .dashboard-page {
            display: flex;
            min-height: 100vh;
            background-color: var(--gray-50);
        }

        .sidebar {
            width: 280px;
            background-color: var(--white);
            border-right: 1px solid var(--gray-200);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            transition: transform var(--transition-normal);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            transition: margin-left var(--transition-normal);
        }

        .sidebar.collapsed + .main-content {
            margin-left: 70px;
        }

        /* Sidebar Styles */
        .sidebar-header {
            padding: var(--spacing-6) var(--spacing-4);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar-header .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            font-weight: 600;
            color: var(--gray-900);
        }

        .sidebar-header .logo i {
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            padding: var(--spacing-2);
            cursor: pointer;
            color: var(--gray-600);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .sidebar-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        .sidebar-menu {
            flex: 1;
            padding: var(--spacing-4) 0;
            overflow-y: auto;
        }

        .menu-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            margin-bottom: var(--spacing-1);
        }

        .menu-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            padding: var(--spacing-3) var(--spacing-4);
            color: var(--gray-700);
            text-decoration: none;
            transition: all var(--transition-fast);
            position: relative;
        }

        .menu-link:hover {
            background-color: var(--gray-50);
            color: var(--primary-color);
            text-decoration: none;
        }

        .menu-item.active .menu-link {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .menu-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar-footer {
            padding: var(--spacing-4);
            border-top: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            flex: 1;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .user-name {
            font-weight: 500;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .user-role {
            color: var(--gray-500);
            font-size: 0.75rem;
            text-transform: capitalize;
        }

        .logout-btn {
            background: none;
            border: none;
            padding: var(--spacing-2);
            cursor: pointer;
            color: var(--gray-600);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .logout-btn:hover {
            background-color: var(--error-color);
            color: var(--white);
        }

        /* Top Header */
        .top-header {
            background-color: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: var(--spacing-4) var(--spacing-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
        }

        .header-btn {
            background: none;
            border: none;
            padding: var(--spacing-2);
            cursor: pointer;
            color: var(--gray-600);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .header-btn:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        /* Dashboard Content */
        .dashboard-content {
            padding: var(--spacing-6);
            flex: 1;
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-6);
            margin-bottom: var(--spacing-8);
        }

        .metric-card {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
            transition: all var(--transition-fast);
        }

        .metric-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: var(--white);
        }

        .metric-icon.success {
            background-color: var(--success-color);
        }

        .metric-icon.warning {
            background-color: var(--warning-color);
        }

        .metric-icon.info {
            background-color: var(--info-color);
        }

        .metric-icon.primary {
            background-color: var(--primary-color);
        }

        .metric-info {
            flex: 1;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 var(--spacing-1) 0;
        }

        .metric-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            margin: 0 0 var(--spacing-2) 0;
        }

        .metric-change {
            font-size: 0.75rem;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .metric-change.positive {
            background-color: #d1fae5;
            color: #065f46;
        }

        .metric-change.negative {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .metric-change.neutral {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        /* Quick Actions */
        .quick-actions {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-8);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin-top: var(--spacing-4);
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-4);
            background-color: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            color: var(--gray-700);
            text-decoration: none;
            transition: all var(--transition-fast);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .action-btn:hover {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
        }

        .action-btn i {
            font-size: 1.125rem;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            padding: var(--spacing-4);
        }

        .modal-content {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-6);
            border-bottom: 1px solid var(--gray-200);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }

        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-400);
            padding: var(--spacing-2);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
        }

        .modal-close:hover {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        .modal-body {
            padding: var(--spacing-6);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-3);
            padding: var(--spacing-6);
            border-top: 1px solid var(--gray-200);
        }

        /* Data Tables */
        .table-container {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            margin-bottom: var(--spacing-8);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-3) var(--spacing-4);
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .data-table th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .data-table td {
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .data-table tr:hover {
            background-color: var(--gray-50);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* Status Badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.success {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-badge.warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-badge.error {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-badge.info {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-badge.admin {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-badge.accountant {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-badge.auditor {
            background-color: #ede9fe;
            color: #5b21b6;
        }

        .status-badge.employee {
            background-color: #f3f4f6;
            color: #374151;
        }

        /* Alert Messages */
        .alert {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-4);
            font-size: 0.875rem;
        }

        .alert.success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert.error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert.warning {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .alert.info {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .alert-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: var(--spacing-1);
            color: inherit;
            opacity: 0.7;
        }

        .alert-close:hover {
            opacity: 1;
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: var(--spacing-4);
        }

        .mt-4 {
            margin-top: var(--spacing-4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-visible {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-content {
                margin: var(--spacing-2);
                max-width: none;
            }
        }
    </style>
</head>
<body id="app">
    <!-- Login Page -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="logo">
                        <i class="fas fa-calculator"></i>
                        <h1>Enterprise Accounting</h1>
                    </div>
                    <p class="subtitle">Secure Financial Management System</p>
                </div>

                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="credential">
                            <i class="fas fa-user"></i>
                            Username or Email
                        </label>
                        <input
                            type="text"
                            id="credential"
                            name="credential"
                            required
                            placeholder="Default: admin"
                            value="admin"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input">
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                placeholder="Default: admin"
                                value="admin"
                            >
                            <button type="button" class="toggle-password" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                </form>

                <div class="alert info mt-4">
                    <span>💡 Demo Mode: Use admin/admin to login. Only admins can create new users.</span>
                </div>

                <!-- Alert Messages -->
                <div class="alert hidden" id="alertMessage">
                    <span class="alert-text"></span>
                    <button class="alert-close" onclick="hideAlert()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Page -->
    <div id="dashboardPage" class="dashboard-page hidden">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-calculator"></i>
                    <span>Enterprise Accounting</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="sidebar-menu">
                <ul class="menu-list">
                    <li class="menu-item active">
                        <a href="#" class="menu-link" onclick="showPage('dashboard')">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('invoices')">
                            <i class="fas fa-file-invoice"></i>
                            <span>Invoices</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('expenses')">
                            <i class="fas fa-receipt"></i>
                            <span>Expenses</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('payroll')">
                            <i class="fas fa-users"></i>
                            <span>Payroll</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('reports')">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li class="menu-item admin-only">
                        <a href="#" class="menu-link" onclick="showPage('users')">
                            <i class="fas fa-users-cog"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                    <li class="menu-item admin-only">
                        <a href="#" class="menu-link" onclick="showPage('settings')">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name" id="userName">System Administrator</span>
                        <span class="user-role" id="userRole">admin</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="top-header">
                <div class="header-left">
                    <h1 class="page-title" id="pageTitle">Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="header-btn" onclick="toggleTheme()">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Page Content Container -->
            <div id="pageContent" class="dashboard-content">
                <!-- Dashboard Content will be loaded here -->
            </div>
        </main>
    </div>

    <!-- Modals -->
    <!-- Create User Modal -->
    <div class="modal-overlay hidden" id="createUserModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Create New User</h3>
                <button class="modal-close" onclick="hideModal('createUserModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createUserForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="newFirstName">First Name</label>
                        <input type="text" id="newFirstName" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="newLastName">Last Name</label>
                        <input type="text" id="newLastName" name="lastName" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newUsername">Username</label>
                    <input type="text" id="newUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="newEmail">Email</label>
                    <input type="email" id="newEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="newPhone">Phone (Optional)</label>
                    <input type="tel" id="newPhone" name="phone">
                </div>
                <div class="form-group">
                    <label for="newRole">Role</label>
                    <select id="newRole" name="role" required>
                        <option value="employee">Employee</option>
                        <option value="accountant">Accountant</option>
                        <option value="manager">Manager</option>
                        <option value="auditor">Auditor</option>
                        <option value="admin">Administrator</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newPassword">Password</label>
                    <input type="password" id="newPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirmNewPassword">Confirm Password</label>
                    <input type="password" id="confirmNewPassword" name="confirmPassword" required>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createUserModal')">Cancel</button>
                <button type="submit" form="createUserForm" class="btn btn-primary">Create User</button>
            </div>
        </div>
    </div>

    <!-- Create Invoice Modal -->
    <div class="modal-overlay hidden" id="createInvoiceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-file-invoice"></i> Create New Invoice</h3>
                <button class="modal-close" onclick="hideModal('createInvoiceModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createInvoiceForm" class="modal-body" onsubmit="handleCreateInvoice(event); return false;">
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoiceCustomer">Customer</label>
                        <select id="invoiceCustomer" name="customerId" required>
                            <option value="">Select Customer</option>
                            <option value="1">ABC Corporation</option>
                            <option value="2">XYZ Ltd</option>
                            <option value="3">Tech Solutions Inc</option>
                            <option value="4">Global Enterprises</option>
                            <option value="5">Innovation Labs</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">Invoice Date</label>
                        <input type="date" id="invoiceDate" name="invoiceDate" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="dueDate">Due Date</label>
                        <input type="date" id="dueDate" name="dueDate" required>
                    </div>
                    <div class="form-group">
                        <label for="invoiceAmount">Amount</label>
                        <input type="number" id="invoiceAmount" name="amount" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="invoiceNotes">Notes</label>
                    <textarea id="invoiceNotes" name="notes" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createInvoiceModal')">Cancel</button>
                <button type="submit" form="createInvoiceForm" class="btn btn-primary">Create Invoice</button>
            </div>
        </div>
    </div>

    <!-- Create Expense Modal -->
    <div class="modal-overlay hidden" id="createExpenseModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-receipt"></i> Add New Expense</h3>
                <button class="modal-close" onclick="hideModal('createExpenseModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createExpenseForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="expenseDate">Expense Date</label>
                        <input type="date" id="expenseDate" name="expenseDate" required>
                    </div>
                    <div class="form-group">
                        <label for="expenseAmount">Amount</label>
                        <input type="number" id="expenseAmount" name="amount" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="expenseCategory">Category</label>
                    <select id="expenseCategory" name="category" required>
                        <option value="">Select Category</option>
                        <option value="office-supplies">Office Supplies</option>
                        <option value="travel">Travel</option>
                        <option value="marketing">Marketing</option>
                        <option value="utilities">Utilities</option>
                        <option value="software">Software</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="expenseDescription">Description</label>
                    <textarea id="expenseDescription" name="description" rows="3" required></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createExpenseModal')">Cancel</button>
                <button type="submit" form="createExpenseForm" class="btn btn-primary">Add Expense</button>
            </div>
        </div>
    </div>

    <!-- Payroll Management Modals -->
    <!-- Calculate Payroll Modal -->
    <div class="modal-overlay hidden" id="calculatePayrollModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-calculator"></i> Calculate Payroll</h3>
                <button class="modal-close" onclick="hideModal('calculatePayrollModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="payPeriodStart">Pay Period Start</label>
                        <input type="date" id="payPeriodStart" required>
                    </div>
                    <div class="form-group">
                        <label for="payPeriodEnd">Pay Period End</label>
                        <input type="date" id="payPeriodEnd" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="payrollType">Payroll Type</label>
                    <select id="payrollType" required>
                        <option value="regular">Regular Payroll</option>
                        <option value="bonus">Bonus Payroll</option>
                        <option value="overtime">Overtime Payroll</option>
                        <option value="final">Final Settlement</option>
                    </select>
                </div>
                <div class="alert info">
                    <span>💡 This will calculate payroll for all active employees for the selected period.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('calculatePayrollModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="calculatePayroll()">Calculate Payroll</button>
            </div>
        </div>
    </div>

    <!-- Employee Management Modal -->
    <div class="modal-overlay hidden" id="employeeManagementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Employee Management</h3>
                <button class="modal-close" onclick="hideModal('employeeManagementModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="empFirstName">First Name</label>
                        <input type="text" id="empFirstName" required>
                    </div>
                    <div class="form-group">
                        <label for="empLastName">Last Name</label>
                        <input type="text" id="empLastName" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="empEmail">Email</label>
                        <input type="email" id="empEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="empPhone">Phone</label>
                        <input type="tel" id="empPhone">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="empPosition">Position</label>
                        <input type="text" id="empPosition" required>
                    </div>
                    <div class="form-group">
                        <label for="empDepartment">Department</label>
                        <select id="empDepartment" required>
                            <option value="">Select Department</option>
                            <option value="accounting">Accounting</option>
                            <option value="hr">Human Resources</option>
                            <option value="it">Information Technology</option>
                            <option value="sales">Sales</option>
                            <option value="marketing">Marketing</option>
                            <option value="operations">Operations</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="empBasicSalary">Basic Salary</label>
                        <input type="number" id="empBasicSalary" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="empHireDate">Hire Date</label>
                        <input type="date" id="empHireDate" required>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('employeeManagementModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveEmployee()">Save Employee</button>
            </div>
        </div>
    </div>

    <!-- Advanced Settings Modals -->
    <!-- Company Settings Modal -->
    <div class="modal-overlay hidden" id="companySettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-building"></i> Company Settings</h3>
                <button class="modal-close" onclick="hideModal('companySettingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-body">
                <div class="form-group">
                    <label for="companyName">Company Name</label>
                    <input type="text" id="companyName" value="Enterprise Accounting Solutions" required>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="companyEmail">Email</label>
                        <input type="email" id="companyEmail" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="companyPhone">Phone</label>
                        <input type="tel" id="companyPhone" value="******-0123">
                    </div>
                </div>
                <div class="form-group">
                    <label for="companyAddress">Address</label>
                    <textarea id="companyAddress" rows="3">123 Business Street, Suite 100
City, State 12345
United States</textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="taxId">Tax ID</label>
                        <input type="text" id="taxId" value="12-3456789">
                    </div>
                    <div class="form-group">
                        <label for="registrationNumber">Registration Number</label>
                        <input type="text" id="registrationNumber" value="REG123456">
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('companySettingsModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveCompanySettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Tax Configuration Modal -->
    <div class="modal-overlay hidden" id="taxConfigModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-percentage"></i> Tax Configuration</h3>
                <button class="modal-close" onclick="hideModal('taxConfigModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="defaultTaxRate">Default Tax Rate (%)</label>
                        <input type="number" id="defaultTaxRate" value="15" step="0.01" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label for="vatRate">VAT/GST Rate (%)</label>
                        <input type="number" id="vatRate" value="10" step="0.01" min="0" max="100">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="incomeTaxRate">Income Tax Rate (%)</label>
                        <input type="number" id="incomeTaxRate" value="20" step="0.01" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label for="socialSecurityRate">Social Security Rate (%)</label>
                        <input type="number" id="socialSecurityRate" value="6.2" step="0.01" min="0" max="100">
                    </div>
                </div>
                <div class="form-group">
                    <label for="taxYear">Tax Year</label>
                    <select id="taxYear">
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                    </select>
                </div>
                <div class="alert info">
                    <span>💡 These rates will be applied to all new transactions and payroll calculations.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('taxConfigModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveTaxConfiguration()">Save Configuration</button>
            </div>
        </div>
    </div>

    <!-- Custom Report Builder Modal -->
    <div class="modal-overlay hidden" id="customReportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-cogs"></i> Custom Report Builder</h3>
                <button class="modal-close" onclick="hideModal('customReportModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="reportName">Report Name</label>
                    <input type="text" id="reportName" placeholder="Enter report name">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="reportDateFrom">Date From</label>
                        <input type="date" id="reportDateFrom">
                    </div>
                    <div class="form-group">
                        <label for="reportDateTo">Date To</label>
                        <input type="date" id="reportDateTo">
                    </div>
                </div>
                <div class="form-group">
                    <label for="reportDataSource">Data Source</label>
                    <select id="reportDataSource" multiple style="height: 120px;">
                        <option value="invoices">Invoices</option>
                        <option value="expenses">Expenses</option>
                        <option value="payments">Payments</option>
                        <option value="customers">Customers</option>
                        <option value="suppliers">Suppliers</option>
                        <option value="payroll">Payroll</option>
                        <option value="taxes">Tax Records</option>
                        <option value="audit">Audit Trail</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="reportFilters">Filters</label>
                    <textarea id="reportFilters" rows="3" placeholder="Enter custom filters (JSON format)"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('customReportModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generateCustomReport()">Generate Report</button>
            </div>
        </div>
    </div>

    <!-- Financial Forecasting Modal -->
    <div class="modal-overlay hidden" id="forecastingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-crystal-ball"></i> Financial Forecasting</h3>
                <button class="modal-close" onclick="hideModal('forecastingModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="forecastPeriod">Forecast Period</label>
                        <select id="forecastPeriod">
                            <option value="3">Next 3 Months</option>
                            <option value="6">Next 6 Months</option>
                            <option value="12">Next 12 Months</option>
                            <option value="24">Next 2 Years</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="forecastModel">Forecasting Model</label>
                        <select id="forecastModel">
                            <option value="linear">Linear Regression</option>
                            <option value="seasonal">Seasonal Analysis</option>
                            <option value="trend">Trend Analysis</option>
                            <option value="advanced">Advanced ML Model</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="forecastMetrics">Metrics to Forecast</label>
                    <select id="forecastMetrics" multiple style="height: 100px;">
                        <option value="revenue">Revenue</option>
                        <option value="expenses">Expenses</option>
                        <option value="profit">Net Profit</option>
                        <option value="cashflow">Cash Flow</option>
                        <option value="payroll">Payroll Costs</option>
                    </select>
                </div>
                <div class="alert info">
                    <span>🔮 AI-powered forecasting uses historical data to predict future financial trends.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('forecastingModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generateForecast()">Generate Forecast</button>
            </div>
        </div>
    </div>

    <!-- Security Settings Modal -->
    <div class="modal-overlay hidden" id="securitySettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-shield-alt"></i> Security Settings</h3>
                <button class="modal-close" onclick="hideModal('securitySettingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Enable Two-Factor Authentication
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Require Strong Passwords
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Enable Audit Logging
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> Enable IP Restrictions
                    </label>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sessionTimeout">Session Timeout (minutes)</label>
                        <input type="number" id="sessionTimeout" value="30" min="5" max="480">
                    </div>
                    <div class="form-group">
                        <label for="maxLoginAttempts">Max Login Attempts</label>
                        <input type="number" id="maxLoginAttempts" value="5" min="3" max="10">
                    </div>
                </div>
                <div class="form-group">
                    <label for="passwordPolicy">Password Policy</label>
                    <select id="passwordPolicy">
                        <option value="basic">Basic (8+ characters)</option>
                        <option value="medium">Medium (8+ chars, mixed case)</option>
                        <option value="strong">Strong (12+ chars, mixed case, numbers, symbols)</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('securitySettingsModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Backup Settings Modal -->
    <div class="modal-overlay hidden" id="backupSettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-database"></i> Backup & Restore</h3>
                <button class="modal-close" onclick="hideModal('backupSettingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Enable Automatic Backups
                    </label>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="backupFrequency">Backup Frequency</label>
                        <select id="backupFrequency">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="backupRetention">Retention Period (days)</label>
                        <input type="number" id="backupRetention" value="30" min="7" max="365">
                    </div>
                </div>
                <div class="form-group">
                    <label for="backupLocation">Backup Location</label>
                    <select id="backupLocation">
                        <option value="local">Local Storage</option>
                        <option value="cloud">Cloud Storage</option>
                        <option value="both">Both Local & Cloud</option>
                    </select>
                </div>
                <div class="alert warning">
                    <span>⚠️ Regular backups are essential for data protection and disaster recovery.</span>
                </div>
                <div style="margin-top: 20px;">
                    <button type="button" class="btn btn-warning" onclick="createBackup()">
                        <i class="fas fa-save"></i> Create Backup Now
                    </button>
                    <button type="button" class="btn btn-info" onclick="restoreBackup()">
                        <i class="fas fa-upload"></i> Restore from Backup
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('backupSettingsModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveBackupSettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Edit Invoice Modal -->
    <div class="modal-overlay hidden" id="editInvoiceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> Edit Invoice</h3>
                <button class="modal-close" onclick="hideModal('editInvoiceModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editInvoiceForm" class="modal-body" onsubmit="window.invoicesManager.handleEditInvoice(event); return false;">
                <input type="hidden" id="editInvoiceId" name="invoiceId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editInvoiceCustomer">Customer</label>
                        <select id="editInvoiceCustomer" name="customerId" required>
                            <option value="">Select Customer</option>
                            <option value="1">ABC Corporation</option>
                            <option value="2">XYZ Ltd</option>
                            <option value="3">Tech Solutions Inc</option>
                            <option value="4">Global Enterprises</option>
                            <option value="5">Innovation Labs</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editInvoiceAmount">Amount</label>
                        <input type="number" id="editInvoiceAmount" name="amount" step="0.01" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editInvoiceDate">Invoice Date</label>
                        <input type="date" id="editInvoiceDate" name="invoiceDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editInvoiceDueDate">Due Date</label>
                        <input type="date" id="editInvoiceDueDate" name="dueDate" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="editInvoiceStatus">Status</label>
                    <select id="editInvoiceStatus" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editInvoiceNotes">Notes</label>
                    <textarea id="editInvoiceNotes" name="notes" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('editInvoiceModal')">Cancel</button>
                <button type="submit" form="editInvoiceForm" class="btn btn-primary">Update Invoice</button>
            </div>
        </div>
    </div>

    <!-- Create Customer Modal -->
    <div class="modal-overlay hidden" id="createCustomerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Add New Customer</h3>
                <button class="modal-close" onclick="hideModal('createCustomerModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createCustomerForm" class="modal-body" onsubmit="window.invoicesManager.handleCreateCustomer(event); return false;">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName">Customer Name</label>
                        <input type="text" id="customerName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="customerEmail">Email</label>
                        <input type="email" id="customerEmail" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerPhone">Phone</label>
                        <input type="tel" id="customerPhone" name="phone">
                    </div>
                </div>
                <div class="form-group">
                    <label for="customerAddress">Address</label>
                    <textarea id="customerAddress" name="address" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createCustomerModal')">Cancel</button>
                <button type="submit" form="createCustomerForm" class="btn btn-primary">Add Customer</button>
            </div>
        </div>
    </div>

    <!-- Edit Customer Modal -->
    <div class="modal-overlay hidden" id="editCustomerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> Edit Customer</h3>
                <button class="modal-close" onclick="hideModal('editCustomerModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editCustomerForm" class="modal-body" onsubmit="window.invoicesManager.handleEditCustomer(event); return false;">
                <input type="hidden" id="editCustomerId" name="customerId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCustomerName">Customer Name</label>
                        <input type="text" id="editCustomerName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="editCustomerEmail">Email</label>
                        <input type="email" id="editCustomerEmail" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCustomerPhone">Phone</label>
                        <input type="tel" id="editCustomerPhone" name="phone">
                    </div>
                </div>
                <div class="form-group">
                    <label for="editCustomerAddress">Address</label>
                    <textarea id="editCustomerAddress" name="address" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('editCustomerModal')">Cancel</button>
                <button type="submit" form="editCustomerForm" class="btn btn-primary">Update Customer</button>
            </div>
        </div>
    </div>

    <!-- Include JavaScript Modules -->
    <script src="JS/Dashboard.js"></script>
    <script src="JS/Invoices.js"></script>
    <script src="JS/Expenses.js"></script>
    <script src="JS/Payroll.js"></script>

    <script>
        // Application State
        let currentUser = null;
        let currentPage = 'dashboard';
        let sessionTimeout = null;
        let sessionWarningTimeout = null;
        const SESSION_DURATION = 30 * 60 * 1000; // 30 minutes
        const SESSION_WARNING_TIME = 5 * 60 * 1000; // 5 minutes before expiry

        let users = [
            { id: 1, firstName: 'System', lastName: 'Administrator', username: 'admin', email: '<EMAIL>', role: 'admin', isActive: true, lastLogin: new Date() },
            { id: 2, firstName: 'John', lastName: 'Manager', username: 'john.manager', email: '<EMAIL>', role: 'manager', isActive: true, lastLogin: new Date(Date.now() - ********) },
            { id: 3, firstName: 'Jane', lastName: 'Accountant', username: 'jane.acc', email: '<EMAIL>', role: 'accountant', isActive: true, lastLogin: new Date(Date.now() - *********) },
            { id: 4, firstName: 'Bob', lastName: 'Auditor', username: 'bob.audit', email: '<EMAIL>', role: 'auditor', isActive: true, lastLogin: new Date(Date.now() - *********) },
            { id: 5, firstName: 'Alice', lastName: 'Employee', username: 'alice.emp', email: '<EMAIL>', role: 'employee', isActive: true, lastLogin: new Date(Date.now() - *********) }
        ];
        let invoices = [
            { id: 1, number: 'INV-001', customer: 'ABC Corporation', amount: 2500, date: '2024-01-15', dueDate: '2024-02-15', status: 'paid' },
            { id: 2, number: 'INV-002', customer: 'XYZ Ltd', amount: 1800, date: '2024-01-20', dueDate: '2024-02-20', status: 'pending' },
            { id: 3, number: 'INV-003', customer: 'Tech Solutions Inc', amount: 3200, date: '2024-01-25', dueDate: '2024-02-25', status: 'overdue' }
        ];
        let expenses = [
            { id: 1, date: '2024-01-10', amount: 150, category: 'office-supplies', description: 'Office supplies purchase', status: 'approved' },
            { id: 2, date: '2024-01-12', amount: 500, category: 'travel', description: 'Business trip to client', status: 'pending' },
            { id: 3, date: '2024-01-15', amount: 200, category: 'software', description: 'Software license renewal', status: 'approved' }
        ];

        // Session Management Functions
        function saveSession(user) {
            const sessionData = {
                user: user,
                loginTime: new Date().getTime(),
                expiryTime: new Date().getTime() + SESSION_DURATION
            };
            localStorage.setItem('accountingSession', JSON.stringify(sessionData));
            localStorage.setItem('lastActivity', new Date().getTime().toString());
        }

        function loadSession() {
            try {
                const sessionData = localStorage.getItem('accountingSession');
                const lastActivity = localStorage.getItem('lastActivity');

                if (!sessionData || !lastActivity) {
                    return null;
                }

                const session = JSON.parse(sessionData);
                const now = new Date().getTime();
                const timeSinceActivity = now - parseInt(lastActivity);

                // Check if session has expired
                if (now > session.expiryTime || timeSinceActivity > SESSION_DURATION) {
                    clearSession();
                    return null;
                }

                return session;
            } catch (error) {
                console.error('Error loading session:', error);
                clearSession();
                return null;
            }
        }

        function clearSession() {
            localStorage.removeItem('accountingSession');
            localStorage.removeItem('lastActivity');
            clearTimeout(sessionTimeout);
            clearTimeout(sessionWarningTimeout);
        }

        function updateActivity() {
            localStorage.setItem('lastActivity', new Date().getTime().toString());
            resetSessionTimeout();
        }

        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            clearTimeout(sessionWarningTimeout);

            // Set warning timeout (5 minutes before expiry)
            sessionWarningTimeout = setTimeout(() => {
                showSessionWarning();
            }, SESSION_DURATION - SESSION_WARNING_TIME);

            // Set logout timeout
            sessionTimeout = setTimeout(() => {
                autoLogout();
            }, SESSION_DURATION);
        }

        function showSessionWarning() {
            if (confirm('Your session will expire in 5 minutes. Click OK to extend your session.')) {
                updateActivity();
                showAlert('Session extended successfully.', 'success');
            }
        }

        function autoLogout() {
            showAlert('Session expired. Please log in again.', 'warning');
            logout();
        }

        // Authentication Functions
        function handleLogin(e) {
            e.preventDefault();

            const credential = document.getElementById('credential').value;
            const password = document.getElementById('password').value;

            // Simple authentication (in real app, this would be server-side)
            if (credential === 'admin' && password === 'admin') {
                currentUser = users.find(u => u.username === 'admin');
                saveSession(currentUser);
                showAlert('Login successful! Welcome Administrator.', 'success');

                setTimeout(() => {
                    document.getElementById('loginPage').classList.add('hidden');
                    document.getElementById('dashboardPage').classList.remove('hidden');
                    updateUserInfo();
                    applyRoleRestrictions();
                    showPage('dashboard');
                    resetSessionTimeout();
                }, 1500);
            } else {
                // Check other users
                const user = users.find(u =>
                    (u.username === credential || u.email === credential) &&
                    password === 'demo' // Simple demo password for all users
                );

                if (user) {
                    currentUser = user;
                    saveSession(currentUser);
                    showAlert(`Login successful! Welcome ${user.firstName}.`, 'success');

                    setTimeout(() => {
                        document.getElementById('loginPage').classList.add('hidden');
                        document.getElementById('dashboardPage').classList.remove('hidden');
                        updateUserInfo();
                        applyRoleRestrictions();
                        showPage('dashboard');
                        resetSessionTimeout();
                    }, 1500);
                } else {
                    showAlert('Invalid credentials. Use admin/admin or any username with password "demo".', 'error');
                }
            }
        }

        function logout() {
            currentUser = null;
            clearSession();
            document.getElementById('dashboardPage').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            showAlert('Logged out successfully.', 'success');

            // Reset form
            document.getElementById('credential').value = 'admin';
            document.getElementById('password').value = 'admin';
        }

        function checkAuthenticationOnLoad() {
            const session = loadSession();
            if (session && session.user) {
                currentUser = session.user;
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('dashboardPage').classList.remove('hidden');
                updateUserInfo();
                applyRoleRestrictions();
                showPage('dashboard');
                resetSessionTimeout();
                return true;
            }
            return false;
        }

        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userName').textContent = `${currentUser.firstName} ${currentUser.lastName}`;
                document.getElementById('userRole').textContent = currentUser.role;
            }
        }

        function applyRoleRestrictions() {
            const adminOnlyElements = document.querySelectorAll('.admin-only');

            if (currentUser && currentUser.role !== 'admin') {
                adminOnlyElements.forEach(element => {
                    element.style.display = 'none';
                });
            } else {
                adminOnlyElements.forEach(element => {
                    element.style.display = '';
                });
            }
        }

        // Page Navigation
        function showPage(pageName) {
            currentPage = pageName;
            document.getElementById('pageTitle').textContent = pageName.charAt(0).toUpperCase() + pageName.slice(1);

            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeMenuItem = document.querySelector(`[onclick="showPage('${pageName}')"]`);
            if (activeMenuItem) {
                activeMenuItem.closest('.menu-item').classList.add('active');
            }

            // Load page content
            loadPageContent(pageName);
        }

        function loadPageContent(pageName) {
            const pageContent = document.getElementById('pageContent');

            switch (pageName) {
                case 'dashboard':
                    pageContent.innerHTML = getDashboardContent();
                    // Initialize dashboard manager
                    if (window.dashboardManager) {
                        window.dashboardManager.init();
                    }
                    break;
                case 'invoices':
                    pageContent.innerHTML = getInvoicesContent();
                    // Initialize invoices manager
                    if (window.invoicesManager) {
                        window.invoicesManager.init();
                    }
                    break;
                case 'expenses':
                    pageContent.innerHTML = getExpensesContent();
                    // Initialize expenses manager
                    if (window.expensesManager) {
                        window.expensesManager.init();
                    }
                    break;
                case 'users':
                    if (currentUser && currentUser.role === 'admin') {
                        pageContent.innerHTML = getUsersContent();
                        loadUsersTable();
                    } else {
                        pageContent.innerHTML = '<div class="alert error">Access Denied: Admin privileges required.</div>';
                    }
                    break;
                case 'payroll':
                    pageContent.innerHTML = getPayrollContent();
                    // Initialize payroll manager
                    if (window.payrollManager) {
                        window.payrollManager.init();
                    }
                    break;
                case 'reports':
                    pageContent.innerHTML = getReportsContent();
                    break;
                case 'settings':
                    if (currentUser && currentUser.role === 'admin') {
                        pageContent.innerHTML = getSettingsContent();
                    } else {
                        pageContent.innerHTML = '<div class="alert error">Access Denied: Admin privileges required.</div>';
                    }
                    break;
                default:
                    pageContent.innerHTML = '<div class="alert info">Page under development.</div>';
            }
        }

        // Page Content Functions
        function getDashboardContent() {
            const roleMessage = currentUser.role !== 'admin' ?
                `<div class="alert warning mb-4">
                    <span>⚠️ ${getRoleDescription(currentUser.role)}</span>
                </div>` : '';

            return `
                ${roleMessage}
                <!-- Key Metrics Cards -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$125,000</h3>
                            <p class="metric-label">Total Revenue</p>
                            <span class="metric-change positive">+12.5%</span>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon warning">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$85,000</h3>
                            <p class="metric-label">Total Expenses</p>
                            <span class="metric-change negative">+8.2%</span>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon info">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$40,000</h3>
                            <p class="metric-label">Net Profit</p>
                            <span class="metric-change positive">+15.3%</span>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon primary">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">12</h3>
                            <p class="metric-label">Pending Invoices</p>
                            <span class="metric-change neutral">-</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>Quick Actions</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('createInvoiceModal')">
                            <i class="fas fa-plus"></i>
                            <span>Create Invoice</span>
                        </button>
                        <button class="action-btn" onclick="showModal('createExpenseModal')">
                            <i class="fas fa-receipt"></i>
                            <span>Add Expense</span>
                        </button>
                        <button class="action-btn ${currentUser.role !== 'admin' ? 'hidden' : ''}" onclick="showModal('createUserModal')">
                            <i class="fas fa-users-cog"></i>
                            <span>Manage Users</span>
                        </button>
                        <button class="action-btn" onclick="showPage('reports')">
                            <i class="fas fa-chart-bar"></i>
                            <span>Generate Report</span>
                        </button>
                    </div>
                </div>

                <!-- Chart Container -->
                <div class="quick-actions">
                    <h3>Financial Overview</h3>
                    <canvas id="dashboardChart" width="400" height="200"></canvas>
                </div>
            `;
        }

        function getInvoicesContent() {
            return `
                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>Invoice Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createInvoiceModal')">
                        <i class="fas fa-plus"></i> Create Invoice
                    </button>
                </div>

                <div class="table-container">
                    <table class="data-table" id="invoicesTable">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTableBody">
                            <!-- Invoices will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Customers Section -->
                <div class="mt-6">
                    <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>Customer Management</h3>
                        <button class="btn btn-primary" onclick="showModal('createCustomerModal')">
                            <i class="fas fa-user-plus"></i> Add Customer
                        </button>
                    </div>
                    <div class="table-container">
                        <table class="data-table" id="customersTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Address</th>
                                    <th>Invoices</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- Customer data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        function getExpensesContent() {
            return `
                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>Expense Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createExpenseModal')">
                        <i class="fas fa-plus"></i> Add Expense
                    </button>
                </div>

                <div class="table-container">
                    <table class="data-table" id="expensesTable">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="expensesTableBody">
                            <!-- Expenses will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getUsersContent() {
            return `
                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>User Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createUserModal')">
                        <i class="fas fa-user-plus"></i> Create User
                    </button>
                </div>

                <div class="alert info mb-4">
                    <span>👑 Administrator Access: You have full user management privileges</span>
                </div>

                <div class="table-container">
                    <table class="data-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getPayrollContent() {
            return `
                <div class="alert info mb-4">
                    <span>💼 Comprehensive Payroll Management System</span>
                </div>

                <!-- Payroll Metrics -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon success">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">45</h3>
                            <p class="metric-label">Active Employees</p>
                            <span class="metric-change positive">+3 this month</span>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon warning">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$125,450</h3>
                            <p class="metric-label">Monthly Payroll</p>
                            <span class="metric-change positive">+5.2%</span>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$18,750</h3>
                            <p class="metric-label">Tax Deductions</p>
                            <span class="metric-change neutral">15% of gross</span>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon primary">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$106,700</h3>
                            <p class="metric-label">Net Payroll</p>
                            <span class="metric-change positive">Ready for processing</span>
                        </div>
                    </div>
                </div>

                <!-- Payroll Actions -->
                <div class="quick-actions">
                    <h3>Payroll Management</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('calculatePayrollModal')">
                            <i class="fas fa-calculator"></i>
                            <span>Calculate Payroll</span>
                        </button>
                        <button class="action-btn" onclick="showModal('generatePayslipsModal')">
                            <i class="fas fa-file-pdf"></i>
                            <span>Generate Payslips</span>
                        </button>
                        <button class="action-btn" onclick="showModal('payrollReportsModal')">
                            <i class="fas fa-chart-pie"></i>
                            <span>Payroll Reports</span>
                        </button>
                        <button class="action-btn" onclick="showModal('employeeManagementModal')">
                            <i class="fas fa-user-plus"></i>
                            <span>Manage Employees</span>
                        </button>
                        <button class="action-btn" onclick="showModal('taxSettingsModal')">
                            <i class="fas fa-cogs"></i>
                            <span>Tax Settings</span>
                        </button>
                        <button class="action-btn" onclick="showModal('payrollHistoryModal')">
                            <i class="fas fa-history"></i>
                            <span>Payroll History</span>
                        </button>
                    </div>
                </div>

                <!-- Employee Payroll Table -->
                <div class="table-container">
                    <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>Current Pay Period</h3>
                        <div>
                            <button class="btn btn-secondary" onclick="exportPayrollData()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-primary" onclick="processPayroll()">
                                <i class="fas fa-play"></i> Process Payroll
                            </button>
                        </div>
                    </div>
                    <table class="data-table" id="payrollTable">
                        <thead>
                            <tr>
                                <th>Employee</th>
                                <th>Position</th>
                                <th>Basic Salary</th>
                                <th>Allowances</th>
                                <th>Overtime</th>
                                <th>Gross Pay</th>
                                <th>Tax</th>
                                <th>Deductions</th>
                                <th>Net Pay</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="payrollTableBody">
                            <!-- Payroll data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getReportsContent() {
            return `
                <div class="alert info mb-4">
                    <span>📊 Advanced Financial Reporting & Analytics Suite</span>
                </div>

                <!-- Report Categories -->
                <div class="quick-actions">
                    <h3>Financial Reports</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="generateReport('profit-loss')">
                            <i class="fas fa-chart-line"></i>
                            <span>Profit & Loss Statement</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('balance-sheet')">
                            <i class="fas fa-balance-scale"></i>
                            <span>Balance Sheet</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('cash-flow')">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Cash Flow Statement</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('trial-balance')">
                            <i class="fas fa-calculator"></i>
                            <span>Trial Balance</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Management Reports</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="generateReport('budget-variance')">
                            <i class="fas fa-chart-bar"></i>
                            <span>Budget vs Actual</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('aging-report')">
                            <i class="fas fa-clock"></i>
                            <span>Accounts Aging</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('expense-analysis')">
                            <i class="fas fa-pie-chart"></i>
                            <span>Expense Analysis</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('revenue-analysis')">
                            <i class="fas fa-trending-up"></i>
                            <span>Revenue Analysis</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Tax & Compliance Reports</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="generateReport('tax-summary')">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>Tax Summary</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('vat-report')">
                            <i class="fas fa-percentage"></i>
                            <span>VAT/GST Report</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('audit-trail')">
                            <i class="fas fa-search"></i>
                            <span>Audit Trail</span>
                        </button>
                        <button class="action-btn" onclick="generateReport('compliance-check')">
                            <i class="fas fa-shield-alt"></i>
                            <span>Compliance Check</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Export & Analytics</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="exportAllData()">
                            <i class="fas fa-file-excel"></i>
                            <span>Export All Data</span>
                        </button>
                        <button class="action-btn" onclick="showModal('customReportModal')">
                            <i class="fas fa-cogs"></i>
                            <span>Custom Report Builder</span>
                        </button>
                        <button class="action-btn" onclick="showModal('forecastingModal')">
                            <i class="fas fa-crystal-ball"></i>
                            <span>Financial Forecasting</span>
                        </button>
                        <button class="action-btn" onclick="showModal('dashboardAnalyticsModal')">
                            <i class="fas fa-chart-area"></i>
                            <span>Advanced Analytics</span>
                        </button>
                    </div>
                </div>

                <!-- Report Display Area -->
                <div class="table-container" id="reportDisplayArea" style="display: none;">
                    <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3 id="reportTitle">Report Results</h3>
                        <div>
                            <button class="btn btn-secondary" onclick="printReport()">
                                <i class="fas fa-print"></i> Print
                            </button>
                            <button class="btn btn-primary" onclick="downloadReport()">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                    <div id="reportContent">
                        <!-- Report content will be displayed here -->
                    </div>
                </div>
            `;
        }

        function getSettingsContent() {
            return `
                <div class="alert success mb-4">
                    <span>⚙️ Enterprise System Configuration & Management</span>
                </div>

                <div class="quick-actions">
                    <h3>Company Configuration</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('companySettingsModal')">
                            <i class="fas fa-building"></i>
                            <span>Company Profile</span>
                        </button>
                        <button class="action-btn" onclick="showModal('fiscalYearModal')">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Fiscal Year Settings</span>
                        </button>
                        <button class="action-btn" onclick="showModal('currencySettingsModal')">
                            <i class="fas fa-dollar-sign"></i>
                            <span>Currency & Localization</span>
                        </button>
                        <button class="action-btn" onclick="showModal('chartOfAccountsModal')">
                            <i class="fas fa-list"></i>
                            <span>Chart of Accounts</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Security & Access Control</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('securitySettingsModal')">
                            <i class="fas fa-shield-alt"></i>
                            <span>Security Policies</span>
                        </button>
                        <button class="action-btn" onclick="showModal('userPermissionsModal')">
                            <i class="fas fa-user-shield"></i>
                            <span>User Permissions</span>
                        </button>
                        <button class="action-btn" onclick="showModal('auditSettingsModal')">
                            <i class="fas fa-search"></i>
                            <span>Audit Configuration</span>
                        </button>
                        <button class="action-btn" onclick="showModal('mfaSettingsModal')">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Multi-Factor Auth</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Tax & Compliance</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('taxConfigModal')">
                            <i class="fas fa-percentage"></i>
                            <span>Tax Configuration</span>
                        </button>
                        <button class="action-btn" onclick="showModal('complianceSettingsModal')">
                            <i class="fas fa-gavel"></i>
                            <span>Compliance Rules</span>
                        </button>
                        <button class="action-btn" onclick="showModal('reportingStandardsModal')">
                            <i class="fas fa-file-contract"></i>
                            <span>Reporting Standards</span>
                        </button>
                        <button class="action-btn" onclick="showModal('regulatorySettingsModal')">
                            <i class="fas fa-balance-scale"></i>
                            <span>Regulatory Settings</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>System Management</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('backupSettingsModal')">
                            <i class="fas fa-database"></i>
                            <span>Backup & Restore</span>
                        </button>
                        <button class="action-btn" onclick="showModal('integrationSettingsModal')">
                            <i class="fas fa-plug"></i>
                            <span>API & Integrations</span>
                        </button>
                        <button class="action-btn" onclick="showModal('notificationSettingsModal')">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                        </button>
                        <button class="action-btn" onclick="showModal('systemPreferencesModal')">
                            <i class="fas fa-cogs"></i>
                            <span>System Preferences</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Advanced Features</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('workflowSettingsModal')">
                            <i class="fas fa-project-diagram"></i>
                            <span>Workflow Automation</span>
                        </button>
                        <button class="action-btn" onclick="showModal('templateManagementModal')">
                            <i class="fas fa-file-alt"></i>
                            <span>Document Templates</span>
                        </button>
                        <button class="action-btn" onclick="showModal('customFieldsModal')">
                            <i class="fas fa-plus-square"></i>
                            <span>Custom Fields</span>
                        </button>
                        <button class="action-btn" onclick="showModal('systemHealthModal')">
                            <i class="fas fa-heartbeat"></i>
                            <span>System Health</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // Data Loading Functions - Removed conflicting invoice functions (now handled by JS modules)

        function loadExpensesTable() {
            const tbody = document.getElementById('expensesTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';
            expenses.forEach(expense => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDate(expense.date)}</td>
                    <td>$${expense.amount.toLocaleString()}</td>
                    <td>${expense.category.replace('-', ' ')}</td>
                    <td>${expense.description}</td>
                    <td><span class="status-badge ${expense.status}">${expense.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="editExpense(${expense.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${currentUser.role === 'admin' || currentUser.role === 'manager' ?
                            `<button class="btn btn-sm btn-success" onclick="approveExpense(${expense.id})">
                                <i class="fas fa-check"></i>
                            </button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function loadUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.firstName} ${user.lastName}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td><span class="status-badge ${user.role}">${user.role}</span></td>
                    <td><span class="status-badge ${user.isActive ? 'success' : 'error'}">${user.isActive ? 'Active' : 'Inactive'}</span></td>
                    <td>${formatDateTime(user.lastLogin)}</td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${user.id !== currentUser.id ?
                            `<button class="btn btn-sm btn-danger" onclick="deactivateUser(${user.id})">
                                <i class="fas fa-ban"></i>
                            </button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Chart Functions
        function loadDashboardCharts() {
            setTimeout(() => {
                const canvas = document.getElementById('dashboardChart');
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                            datasets: [{
                                label: 'Revenue',
                                data: [12000, 15000, 18000, 14000, 20000, 22000],
                                borderColor: '#2563eb',
                                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                tension: 0.4
                            }, {
                                label: 'Expenses',
                                data: [8000, 9000, 11000, 10000, 13000, 15000],
                                borderColor: '#f59e0b',
                                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return '$' + value.toLocaleString();
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            }, 100);
        }

        // Modal Functions (Basic - Enhanced versions are defined later)

        // Form Handlers
        function handleCreateUser(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                id: users.length + 1,
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                username: formData.get('username'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                isActive: true,
                lastLogin: null
            };

            // Validate passwords match
            if (formData.get('password') !== formData.get('confirmPassword')) {
                showAlert('Passwords do not match', 'error');
                return;
            }

            // Check if username/email already exists
            if (users.some(u => u.username === userData.username || u.email === userData.email)) {
                showAlert('Username or email already exists', 'error');
                return;
            }

            users.push(userData);
            hideModal('createUserModal');
            showAlert('User created successfully', 'success');

            if (currentPage === 'users') {
                loadUsersTable();
            }
        }

        // Invoice creation now handled by JS/Invoices.js module

        function handleCreateExpense(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const expenseData = {
                id: expenses.length + 1,
                date: formData.get('expenseDate'),
                amount: parseFloat(formData.get('amount')),
                category: formData.get('category'),
                description: formData.get('description'),
                status: 'pending'
            };

            expenses.push(expenseData);
            hideModal('createExpenseModal');
            showAlert('Expense added successfully', 'success');

            if (currentPage === 'expenses') {
                loadExpensesTable();
            }
        }

        // Utility Functions
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function formatDateTime(date) {
            if (!date) return 'Never';
            return new Date(date).toLocaleString();
        }

        function getRoleDescription(role) {
            const descriptions = {
                admin: 'Full system access - no restrictions',
                manager: 'Management access - cannot manage users or system settings',
                accountant: 'Accounting access - cannot approve expenses or manage users',
                auditor: 'Read-only access - cannot modify any data',
                employee: 'Limited access - can only view own data'
            };
            return descriptions[role] || 'Unknown role';
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }

        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');

            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'fas fa-moon';
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
            }
        }

        function showAlert(message, type = 'info') {
            const alertElement = document.getElementById('alertMessage');
            if (!alertElement) return;

            const alertText = alertElement.querySelector('.alert-text');
            alertText.textContent = message;

            alertElement.className = `alert ${type}`;
            alertElement.classList.remove('hidden');

            if (type === 'success') {
                setTimeout(() => {
                    alertElement.classList.add('hidden');
                }, 3000);
            }
        }

        function hideAlert() {
            const alertElement = document.getElementById('alertMessage');
            if (alertElement) {
                alertElement.classList.add('hidden');
            }
        }

        // Enhanced Action Functions - Invoice functions now handled by JS/Invoices.js module

        function editExpense(id) {
            showAlert('Opening expense editor with receipt management...', 'info');
            updateActivity();
        }

        function approveExpense(id) {
            showAlert('Expense approved successfully. Notification sent to employee.', 'success');
            updateActivity();
        }

        function editUser(id) {
            showAlert('Opening user profile with advanced permissions...', 'info');
            updateActivity();
        }

        function deactivateUser(id) {
            if (confirm('Are you sure you want to deactivate this user?')) {
                showAlert('User deactivated successfully. Access revoked immediately.', 'success');
                updateActivity();
            }
        }

        // Advanced Payroll Functions
        function calculatePayroll() {
            showAlert('Calculating payroll with tax deductions and benefits...', 'info');
            setTimeout(() => {
                showAlert('Payroll calculated successfully! Review before processing.', 'success');
                hideModal('calculatePayrollModal');
            }, 2000);
            updateActivity();
        }

        function saveEmployee() {
            const firstName = document.getElementById('empFirstName').value;
            const lastName = document.getElementById('empLastName').value;

            if (firstName && lastName) {
                showAlert(`Employee ${firstName} ${lastName} added successfully!`, 'success');
                hideModal('employeeManagementModal');
            } else {
                showAlert('Please fill in all required fields.', 'error');
            }
            updateActivity();
        }

        // Advanced Settings Functions
        function saveCompanySettings() {
            showAlert('Company settings saved successfully!', 'success');
            hideModal('companySettingsModal');
            updateActivity();
        }

        function saveTaxConfiguration() {
            showAlert('Tax configuration updated successfully!', 'success');
            hideModal('taxConfigModal');
            updateActivity();
        }

        function generateCustomReport() {
            const reportName = document.getElementById('reportName').value;
            if (reportName) {
                showAlert(`Generating custom report: ${reportName}...`, 'info');
                setTimeout(() => {
                    showAlert('Custom report generated successfully!', 'success');
                    hideModal('customReportModal');
                }, 2000);
            } else {
                showAlert('Please enter a report name.', 'error');
            }
            updateActivity();
        }

        // Advanced Financial Functions
        function loadPayrollTable() {
            const tbody = document.getElementById('payrollTableBody');
            if (!tbody) return;

            const payrollData = [
                { id: 1, name: 'John Smith', position: 'Senior Accountant', basic: 5000, allowances: 500, overtime: 200, tax: 850, deductions: 100, status: 'pending' },
                { id: 2, name: 'Jane Doe', position: 'Financial Analyst', basic: 4500, allowances: 400, overtime: 150, tax: 765, deductions: 50, status: 'approved' },
                { id: 3, name: 'Mike Johnson', position: 'Accounts Manager', basic: 6000, allowances: 600, overtime: 300, tax: 1035, deductions: 150, status: 'pending' },
                { id: 4, name: 'Sarah Wilson', position: 'Junior Accountant', basic: 3500, allowances: 300, overtime: 100, tax: 585, deductions: 75, status: 'draft' },
                { id: 5, name: 'David Brown', position: 'Payroll Specialist', basic: 4000, allowances: 350, overtime: 120, tax: 670, deductions: 80, status: 'approved' }
            ];

            tbody.innerHTML = '';
            payrollData.forEach(emp => {
                const gross = emp.basic + emp.allowances + emp.overtime;
                const net = gross - emp.tax - emp.deductions;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${emp.name}</td>
                    <td>${emp.position}</td>
                    <td>$${emp.basic.toLocaleString()}</td>
                    <td>$${emp.allowances.toLocaleString()}</td>
                    <td>$${emp.overtime.toLocaleString()}</td>
                    <td>$${gross.toLocaleString()}</td>
                    <td>$${emp.tax.toLocaleString()}</td>
                    <td>$${emp.deductions.toLocaleString()}</td>
                    <td>$${net.toLocaleString()}</td>
                    <td><span class="status-badge ${emp.status}">${emp.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="editPayroll(${emp.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="approvePayroll(${emp.id})">
                            <i class="fas fa-check"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function editPayroll(id) {
            showAlert('Opening payroll editor with detailed breakdown...', 'info');
            updateActivity();
        }

        function approvePayroll(id) {
            showAlert('Payroll entry approved successfully!', 'success');
            updateActivity();
        }

        // Advanced Modal Functions
        function generateForecast() {
            const period = document.getElementById('forecastPeriod').value;
            const model = document.getElementById('forecastModel').value;

            showAlert(`Generating ${period}-month forecast using ${model} model...`, 'info');
            setTimeout(() => {
                showAlert('Financial forecast generated successfully! Check reports section.', 'success');
                hideModal('forecastingModal');
            }, 3000);
            updateActivity();
        }

        function saveSecuritySettings() {
            showAlert('Security settings updated successfully!', 'success');
            hideModal('securitySettingsModal');
            updateActivity();
        }

        function saveBackupSettings() {
            showAlert('Backup settings saved successfully!', 'success');
            hideModal('backupSettingsModal');
            updateActivity();
        }

        function createBackup() {
            showAlert('Creating system backup...', 'info');
            setTimeout(() => {
                showAlert('Backup created successfully! Stored securely.', 'success');
            }, 2000);
            updateActivity();
        }

        function restoreBackup() {
            if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {
                showAlert('Restoring from backup...', 'info');
                setTimeout(() => {
                    showAlert('System restored successfully from backup!', 'success');
                }, 3000);
            }
            updateActivity();
        }

        // Enhanced Dashboard with Real-time Updates
        function updateDashboardMetrics() {
            // Simulate real-time data updates
            const metrics = [
                { id: 'totalRevenue', value: '$' + (125000 + Math.floor(Math.random() * 5000)).toLocaleString() },
                { id: 'totalExpenses', value: '$' + (85000 + Math.floor(Math.random() * 3000)).toLocaleString() },
                { id: 'netProfit', value: '$' + (40000 + Math.floor(Math.random() * 2000)).toLocaleString() },
                { id: 'pendingInvoices', value: (12 + Math.floor(Math.random() * 5)).toString() }
            ];

            metrics.forEach(metric => {
                const element = document.querySelector(`[data-metric="${metric.id}"]`);
                if (element) {
                    element.textContent = metric.value;
                }
            });
        }

        // Auto-refresh dashboard every 5 minutes
        function startDashboardAutoRefresh() {
            setInterval(() => {
                if (currentPage === 'dashboard' && currentUser) {
                    updateDashboardMetrics();
                    console.log('Dashboard metrics updated automatically');
                }
            }, 5 * 60 * 1000); // 5 minutes
        }

        // Enhanced Error Handling and Logging
        function logUserAction(action, details = {}) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                user: currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Anonymous',
                action: action,
                details: details,
                page: currentPage
            };

            console.log('User Action:', logEntry);
            // In production, this would send to audit logging service
        }

        // Enhanced Modal Management
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');

                // Set default dates for forms
                if (modalId === 'createInvoiceModal') {
                    document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
                    const dueDate = new Date();
                    dueDate.setDate(dueDate.getDate() + 30);
                    document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
                } else if (modalId === 'createExpenseModal') {
                    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
                }

                logUserAction('modal_opened', { modalId });
                updateActivity();
            }
        }

        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');

                // Reset forms
                const form = modal.querySelector('form');
                if (form) form.reset();

                logUserAction('modal_closed', { modalId });
                updateActivity();
            }
        }

        // New Deep Functionality Functions
        function generateReport(reportType) {
            showAlert(`Generating ${reportType} report...`, 'info');
            const reportArea = document.getElementById('reportDisplayArea');
            const reportTitle = document.getElementById('reportTitle');
            const reportContent = document.getElementById('reportContent');

            reportTitle.textContent = `${reportType.replace('-', ' ').toUpperCase()} Report`;
            reportContent.innerHTML = `
                <div class="alert info">
                    <span>📊 ${reportType} report generated successfully. This would contain detailed financial data in a production environment.</span>
                </div>
                <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; margin-top: 20px;">
                    <h4>Sample Report Data</h4>
                    <p>Report Period: ${new Date().toLocaleDateString()} - ${new Date().toLocaleDateString()}</p>
                    <p>Generated by: ${currentUser.firstName} ${currentUser.lastName}</p>
                    <p>Status: Complete</p>
                </div>
            `;
            reportArea.style.display = 'block';
            updateActivity();
        }

        function processPayroll() {
            showAlert('Processing payroll for all employees...', 'info');
            setTimeout(() => {
                showAlert('Payroll processed successfully! Payslips are ready for distribution.', 'success');
            }, 2000);
            updateActivity();
        }

        function exportPayrollData() {
            showAlert('Exporting payroll data to Excel...', 'info');
            setTimeout(() => {
                showAlert('Payroll data exported successfully!', 'success');
            }, 1500);
            updateActivity();
        }

        function exportAllData() {
            showAlert('Exporting all financial data...', 'info');
            setTimeout(() => {
                showAlert('All data exported successfully!', 'success');
            }, 2000);
            updateActivity();
        }

        function printReport() {
            window.print();
            updateActivity();
        }

        function downloadReport() {
            showAlert('Downloading report...', 'info');
            setTimeout(() => {
                showAlert('Report downloaded successfully!', 'success');
            }, 1000);
            updateActivity();
        }

        // Activity tracking for session management
        function setupActivityTracking() {
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
            events.forEach(event => {
                document.addEventListener(event, updateActivity, true);
            });
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', function() {
            // Check for existing session first
            if (!checkAuthenticationOnLoad()) {
                // No valid session, show login page
                document.getElementById('loginPage').classList.remove('hidden');
                document.getElementById('dashboardPage').classList.add('hidden');
            }

            // Setup form handlers
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);
            // Invoice form handler is now managed by JS/Invoices.js
            document.getElementById('createExpenseForm').addEventListener('submit', handleCreateExpense);

            // Setup sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('collapsed');
            });

            // Close modals when clicking outside
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal-overlay')) {
                    e.target.classList.add('hidden');
                }
            });

            // Setup activity tracking for session management
            setupActivityTracking();

            // Start dashboard auto-refresh
            startDashboardAutoRefresh();

            // Log application startup
            logUserAction('application_started');

            // Set current date for date inputs
            const today = new Date().toISOString().split('T')[0];
            const dateInputs = document.querySelectorAll('input[type="date"]');
            dateInputs.forEach(input => {
                if (!input.value) {
                    input.value = today;
                }
            });
        });
    </script>
</body>
</html>