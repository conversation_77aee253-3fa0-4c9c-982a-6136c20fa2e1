/**
 * Invoices.js - Comprehensive Invoice Management System
 * Manages all invoice operations including adding, editing, removing with real-time updates
 */

class InvoicesManager {
    constructor() {
        this.invoices = [];
        this.customers = [];
        this.nextInvoiceNumber = 1;
        this.isInitialized = false;
        this.currentFilter = 'all';
        this.currentSort = 'date';
        this.sortDirection = 'desc';
    }

    /**
     * Initialize the invoice management system
     */
    init() {
        console.log('Initializing Invoice Manager...');
        if (this.isInitialized) {
            console.log('Invoice Manager already initialized');
            return;
        }

        this.loadData();
        this.setupEventListeners();
        this.loadInvoicesTable();
        this.loadCustomersTable();
        this.updateInvoiceMetrics();
        this.setupRealTimeUpdates();

        this.isInitialized = true;
        console.log('Invoice Manager initialized successfully');
        console.log('Invoices loaded:', this.invoices.length);
        console.log('Customers loaded:', this.customers.length);
    }

    /**
     * Load data from localStorage or initialize with sample data
     */
    loadData() {
        // Load invoices
        const savedInvoices = localStorage.getItem('accounting_invoices');
        if (savedInvoices) {
            this.invoices = JSON.parse(savedInvoices);
            this.nextInvoiceNumber = Math.max(...this.invoices.map(inv => parseInt(inv.number.split('-')[1])), 0) + 1;
        } else {
            this.initializeSampleData();
        }

        // Load customers
        const savedCustomers = localStorage.getItem('accounting_customers');
        if (savedCustomers) {
            this.customers = JSON.parse(savedCustomers);
        } else {
            this.initializeSampleCustomers();
        }
    }

    /**
     * Initialize with sample data
     */
    initializeSampleData() {
        this.invoices = [
            {
                id: 1,
                number: 'INV-001',
                customerId: 1,
                customerName: 'ABC Corporation',
                amount: 5000,
                date: '2024-06-01',
                dueDate: '2024-07-01',
                status: 'paid',
                items: [
                    { description: 'Consulting Services', quantity: 10, rate: 500, amount: 5000 }
                ],
                subtotal: 5000,
                tax: 750,
                total: 5750,
                notes: 'Monthly consulting services',
                createdAt: new Date('2024-06-01'),
                updatedAt: new Date('2024-06-01')
            },
            {
                id: 2,
                number: 'INV-002',
                customerId: 2,
                customerName: 'XYZ Ltd',
                amount: 3500,
                date: '2024-06-05',
                dueDate: '2024-07-05',
                status: 'pending',
                items: [
                    { description: 'Software Development', quantity: 35, rate: 100, amount: 3500 }
                ],
                subtotal: 3500,
                tax: 525,
                total: 4025,
                notes: 'Custom software development',
                createdAt: new Date('2024-06-05'),
                updatedAt: new Date('2024-06-05')
            },
            {
                id: 3,
                number: 'INV-003',
                customerId: 3,
                customerName: 'Tech Solutions Inc',
                amount: 7500,
                date: '2024-06-10',
                dueDate: '2024-07-10',
                status: 'overdue',
                items: [
                    { description: 'System Integration', quantity: 1, rate: 7500, amount: 7500 }
                ],
                subtotal: 7500,
                tax: 1125,
                total: 8625,
                notes: 'Complete system integration project',
                createdAt: new Date('2024-06-10'),
                updatedAt: new Date('2024-06-10')
            }
        ];
        this.nextInvoiceNumber = 4;
        this.saveData();
    }

    /**
     * Initialize sample customers
     */
    initializeSampleCustomers() {
        this.customers = [
            { id: 1, name: 'ABC Corporation', email: '<EMAIL>', phone: '******-0101' },
            { id: 2, name: 'XYZ Ltd', email: '<EMAIL>', phone: '******-0102' },
            { id: 3, name: 'Tech Solutions Inc', email: '<EMAIL>', phone: '******-0103' },
            { id: 4, name: 'Global Enterprises', email: '<EMAIL>', phone: '******-0104' },
            { id: 5, name: 'Innovation Labs', email: '<EMAIL>', phone: '******-0105' }
        ];
        this.saveCustomers();
    }

    /**
     * Save data to localStorage
     */
    saveData() {
        localStorage.setItem('accounting_invoices', JSON.stringify(this.invoices));
        this.updateInvoiceMetrics();
        this.notifyDashboard();
    }

    /**
     * Save customers to localStorage
     */
    saveCustomers() {
        localStorage.setItem('accounting_customers', JSON.stringify(this.customers));
    }

    /**
     * Create new invoice
     */
    createInvoice(invoiceData) {
        const newInvoice = {
            id: Date.now(),
            number: `INV-${String(this.nextInvoiceNumber).padStart(3, '0')}`,
            customerId: parseInt(invoiceData.customerId),
            customerName: this.getCustomerName(invoiceData.customerId),
            amount: parseFloat(invoiceData.amount),
            date: invoiceData.date,
            dueDate: invoiceData.dueDate,
            status: 'pending',
            items: invoiceData.items || [],
            subtotal: parseFloat(invoiceData.amount),
            tax: parseFloat(invoiceData.amount) * 0.15, // 15% tax
            total: parseFloat(invoiceData.amount) * 1.15,
            notes: invoiceData.notes || '',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.invoices.unshift(newInvoice);
        this.nextInvoiceNumber++;
        this.saveData();
        this.loadInvoicesTable();
        
        return newInvoice;
    }

    /**
     * Update existing invoice
     */
    updateInvoice(invoiceId, updateData) {
        const index = this.invoices.findIndex(inv => inv.id === invoiceId);
        if (index === -1) return false;

        const invoice = this.invoices[index];
        
        // Update fields
        Object.keys(updateData).forEach(key => {
            if (key !== 'id' && key !== 'number' && key !== 'createdAt') {
                invoice[key] = updateData[key];
            }
        });

        // Recalculate totals if amount changed
        if (updateData.amount) {
            invoice.subtotal = parseFloat(updateData.amount);
            invoice.tax = invoice.subtotal * 0.15;
            invoice.total = invoice.subtotal + invoice.tax;
        }

        invoice.updatedAt = new Date();
        
        this.saveData();
        this.loadInvoicesTable();
        
        return invoice;
    }

    /**
     * Delete invoice
     */
    deleteInvoice(invoiceId) {
        const index = this.invoices.findIndex(inv => inv.id === invoiceId);
        if (index === -1) return false;

        this.invoices.splice(index, 1);
        this.saveData();
        this.loadInvoicesTable();
        
        return true;
    }

    /**
     * Update invoice status
     */
    updateInvoiceStatus(invoiceId, newStatus) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return false;

        invoice.status = newStatus;
        invoice.updatedAt = new Date();
        
        if (newStatus === 'paid') {
            invoice.paidDate = new Date();
        }

        this.saveData();
        this.loadInvoicesTable();
        
        return true;
    }

    /**
     * Get customer name by ID
     */
    getCustomerName(customerId) {
        const customer = this.customers.find(c => c.id === parseInt(customerId));
        return customer ? customer.name : 'Unknown Customer';
    }

    /**
     * Load and display invoices table
     */
    loadInvoicesTable() {
        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) return;

        let filteredInvoices = this.getFilteredInvoices();
        filteredInvoices = this.getSortedInvoices(filteredInvoices);

        tbody.innerHTML = '';
        
        if (filteredInvoices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">No invoices found</td>
                </tr>
            `;
            return;
        }

        filteredInvoices.forEach(invoice => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${invoice.number}</td>
                <td>${invoice.customerName}</td>
                <td>$${invoice.total.toLocaleString()}</td>
                <td>${new Date(invoice.date).toLocaleDateString()}</td>
                <td>${new Date(invoice.dueDate).toLocaleDateString()}</td>
                <td><span class="status-badge ${invoice.status}">${invoice.status}</span></td>
                <td>
                    ${this.isOverdue(invoice) ? '<span class="overdue-badge">Overdue</span>' : ''}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="invoicesManager.viewInvoice(${invoice.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="invoicesManager.editInvoice(${invoice.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="invoicesManager.markAsPaid(${invoice.id})" title="Mark as Paid">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="invoicesManager.sendInvoice(${invoice.id})" title="Send">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="invoicesManager.deleteInvoiceConfirm(${invoice.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.updateInvoiceCount(filteredInvoices.length);
    }

    /**
     * Get filtered invoices based on current filter
     */
    getFilteredInvoices() {
        if (this.currentFilter === 'all') {
            return this.invoices;
        }
        return this.invoices.filter(invoice => invoice.status === this.currentFilter);
    }

    /**
     * Get sorted invoices
     */
    getSortedInvoices(invoices) {
        return invoices.sort((a, b) => {
            let aValue = a[this.currentSort];
            let bValue = b[this.currentSort];

            // Handle date sorting
            if (this.currentSort === 'date' || this.currentSort === 'dueDate') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            // Handle numeric sorting
            if (this.currentSort === 'amount' || this.currentSort === 'total') {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            }

            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    /**
     * Check if invoice is overdue
     */
    isOverdue(invoice) {
        if (invoice.status === 'paid') return false;
        return new Date(invoice.dueDate) < new Date();
    }

    /**
     * Update invoice metrics
     */
    updateInvoiceMetrics() {
        const totalInvoices = this.invoices.length;
        const paidInvoices = this.invoices.filter(inv => inv.status === 'paid').length;
        const pendingInvoices = this.invoices.filter(inv => inv.status === 'pending').length;
        const overdueInvoices = this.invoices.filter(inv => this.isOverdue(inv)).length;
        
        const totalRevenue = this.invoices
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + inv.total, 0);
        
        const pendingAmount = this.invoices
            .filter(inv => inv.status === 'pending')
            .reduce((sum, inv) => sum + inv.total, 0);

        // Update metric cards if they exist
        this.updateMetricCard('totalInvoices', totalInvoices);
        this.updateMetricCard('paidInvoices', paidInvoices);
        this.updateMetricCard('pendingInvoices', pendingInvoices);
        this.updateMetricCard('overdueInvoices', overdueInvoices);
        this.updateMetricCard('totalRevenue', `$${totalRevenue.toLocaleString()}`);
        this.updateMetricCard('pendingAmount', `$${pendingAmount.toLocaleString()}`);
    }

    /**
     * Update metric card
     */
    updateMetricCard(metricId, value) {
        const element = document.getElementById(metricId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * Update invoice count display
     */
    updateInvoiceCount(count) {
        const countElement = document.getElementById('invoiceCount');
        if (countElement) {
            countElement.textContent = `${count} invoice${count !== 1 ? 's' : ''}`;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // Sort headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                this.setSorting(e.target.dataset.sort);
            });
        });

        // Search input
        const searchInput = document.getElementById('invoiceSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchInvoices(e.target.value);
            });
        }

        // Create invoice form
        const createForm = document.getElementById('createInvoiceForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateInvoice(e);
            });
        } else {
            // Try again after a short delay if form not found
            setTimeout(() => {
                const createFormDelayed = document.getElementById('createInvoiceForm');
                if (createFormDelayed) {
                    createFormDelayed.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCreateInvoice(e);
                    });
                }
            }, 100);
        }

        // Edit invoice form
        const editForm = document.getElementById('editInvoiceForm');
        if (editForm) {
            editForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditInvoice(e);
            });
        }

        // Create customer form
        const createCustomerForm = document.getElementById('createCustomerForm');
        if (createCustomerForm) {
            createCustomerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateCustomer(e);
            });
        }

        // Edit customer form
        const editCustomerForm = document.getElementById('editCustomerForm');
        if (editCustomerForm) {
            editCustomerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditCustomer(e);
            });
        }
    }

    /**
     * Set current filter
     */
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        this.loadInvoicesTable();
    }

    /**
     * Set sorting
     */
    setSorting(sortField) {
        if (this.currentSort === sortField) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort = sortField;
            this.sortDirection = 'desc';
        }
        
        this.loadInvoicesTable();
    }

    /**
     * Search invoices
     */
    searchInvoices(query) {
        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) return;

        const filteredInvoices = this.invoices.filter(invoice => 
            invoice.number.toLowerCase().includes(query.toLowerCase()) ||
            invoice.customerName.toLowerCase().includes(query.toLowerCase()) ||
            invoice.status.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(filteredInvoices);
    }

    /**
     * Display search results
     */
    displaySearchResults(invoices) {
        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        invoices.forEach(invoice => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${invoice.number}</td>
                <td>${invoice.customerName}</td>
                <td>$${invoice.total.toLocaleString()}</td>
                <td>${new Date(invoice.date).toLocaleDateString()}</td>
                <td>${new Date(invoice.dueDate).toLocaleDateString()}</td>
                <td><span class="status-badge ${invoice.status}">${invoice.status}</span></td>
                <td>
                    ${this.isOverdue(invoice) ? '<span class="overdue-badge">Overdue</span>' : ''}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="invoicesManager.viewInvoice(${invoice.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="invoicesManager.editInvoice(${invoice.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="invoicesManager.markAsPaid(${invoice.id})">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="invoicesManager.deleteInvoiceConfirm(${invoice.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.updateInvoiceCount(invoices.length);
    }

    /**
     * Handle create invoice form submission
     */
    handleCreateInvoice(event) {
        console.log('Invoice creation started');
        event.preventDefault();

        const formData = new FormData(event.target);
        const invoiceData = Object.fromEntries(formData);

        console.log('Form data:', invoiceData);

        // Validation
        if (!invoiceData.customerId || !invoiceData.amount || !invoiceData.invoiceDate || !invoiceData.dueDate) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        if (parseFloat(invoiceData.amount) <= 0) {
            this.showAlert('Invoice amount must be greater than zero.', 'error');
            return;
        }

        // Fix the data structure
        const processedData = {
            customerId: invoiceData.customerId,
            amount: invoiceData.amount,
            date: invoiceData.invoiceDate,
            dueDate: invoiceData.dueDate,
            notes: invoiceData.notes || ''
        };

        console.log('Processed data:', processedData);

        const newInvoice = this.createInvoice(processedData);
        this.showAlert(`Invoice ${newInvoice.number} created successfully!`, 'success');

        // Reset form and close modal
        event.target.reset();
        this.hideModal('createInvoiceModal');
    }

    /**
     * Setup real-time updates
     */
    setupRealTimeUpdates() {
        // Auto-save every 30 seconds
        setInterval(() => {
            this.saveData();
        }, 30000);

        // Check for overdue invoices every hour
        setInterval(() => {
            this.checkOverdueInvoices();
        }, 3600000);
    }

    /**
     * Check for overdue invoices and notify
     */
    checkOverdueInvoices() {
        const overdueInvoices = this.invoices.filter(inv => this.isOverdue(inv));
        if (overdueInvoices.length > 0) {
            console.log(`${overdueInvoices.length} overdue invoices found`);
            // Could trigger notifications here
        }
    }

    /**
     * View invoice details
     */
    viewInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        // Implementation for viewing invoice details
        console.log('Viewing invoice:', invoice);
        this.showAlert('Invoice details view would open here.', 'info');
    }

    /**
     * Edit invoice
     */
    editInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        // Populate edit form
        this.populateEditForm(invoice);
        this.showModal('editInvoiceModal');
    }

    /**
     * Populate edit form with invoice data
     */
    populateEditForm(invoice) {
        document.getElementById('editInvoiceId').value = invoice.id;
        document.getElementById('editInvoiceCustomer').value = invoice.customerId;
        document.getElementById('editInvoiceAmount').value = invoice.amount;
        document.getElementById('editInvoiceDate').value = invoice.date;
        document.getElementById('editInvoiceDueDate').value = invoice.dueDate;
        document.getElementById('editInvoiceStatus').value = invoice.status;
        document.getElementById('editInvoiceNotes').value = invoice.notes || '';
    }

    /**
     * Handle edit invoice form submission
     */
    handleEditInvoice(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const invoiceData = Object.fromEntries(formData);

        const invoiceId = parseInt(invoiceData.invoiceId);

        // Validation
        if (!invoiceData.customerId || !invoiceData.amount || !invoiceData.invoiceDate || !invoiceData.dueDate) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        if (parseFloat(invoiceData.amount) <= 0) {
            this.showAlert('Invoice amount must be greater than zero.', 'error');
            return;
        }

        // Update invoice
        const updateData = {
            customerId: parseInt(invoiceData.customerId),
            customerName: this.getCustomerName(invoiceData.customerId),
            amount: parseFloat(invoiceData.amount),
            date: invoiceData.invoiceDate,
            dueDate: invoiceData.dueDate,
            status: invoiceData.status,
            notes: invoiceData.notes || ''
        };

        const updatedInvoice = this.updateInvoice(invoiceId, updateData);
        if (updatedInvoice) {
            this.showAlert(`Invoice ${updatedInvoice.number} updated successfully!`, 'success');
            this.hideModal('editInvoiceModal');
        } else {
            this.showAlert('Failed to update invoice.', 'error');
        }
    }

    /**
     * Mark invoice as paid
     */
    markAsPaid(invoiceId) {
        if (this.updateInvoiceStatus(invoiceId, 'paid')) {
            this.showAlert('Invoice marked as paid successfully!', 'success');
        }
    }

    /**
     * Send invoice
     */
    sendInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        // Implementation for sending invoice
        console.log('Sending invoice:', invoice);
        this.showAlert('Invoice sent successfully!', 'success');
    }

    /**
     * Delete invoice with confirmation
     */
    deleteInvoiceConfirm(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        if (confirm(`Are you sure you want to delete invoice ${invoice.number}? This action cannot be undone.`)) {
            if (this.deleteInvoice(invoiceId)) {
                this.showAlert('Invoice deleted successfully!', 'success');
            } else {
                this.showAlert('Failed to delete invoice.', 'error');
            }
        }
    }

    /**
     * Get all invoices (for dashboard integration)
     */
    getAllInvoices() {
        return this.invoices;
    }

    /**
     * Show alert message
     */
    showAlert(message, type) {
        // Implementation depends on your alert system
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // If you have a global showAlert function, use it
        if (window.showAlert) {
            window.showAlert(message, type);
        }
    }

    /**
     * Show modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    /**
     * Hide modal
     */
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Customer Management Functions
     */

    /**
     * Create new customer
     */
    createCustomer(customerData) {
        const newCustomer = {
            id: Math.max(...this.customers.map(c => c.id), 0) + 1,
            name: customerData.name,
            email: customerData.email,
            phone: customerData.phone || '',
            address: customerData.address || '',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.customers.push(newCustomer);
        this.saveCustomers();
        this.loadCustomersTable();

        return newCustomer;
    }

    /**
     * Update existing customer
     */
    updateCustomer(customerId, updateData) {
        const index = this.customers.findIndex(c => c.id === customerId);
        if (index === -1) return false;

        const customer = this.customers[index];

        // Update fields
        Object.keys(updateData).forEach(key => {
            if (key !== 'id' && key !== 'createdAt') {
                customer[key] = updateData[key];
            }
        });

        customer.updatedAt = new Date();

        this.saveCustomers();
        this.loadCustomersTable();

        return customer;
    }

    /**
     * Delete customer
     */
    deleteCustomer(customerId) {
        // Check if customer has invoices
        const hasInvoices = this.invoices.some(inv => inv.customerId === customerId);
        if (hasInvoices) {
            this.showAlert('Cannot delete customer with existing invoices.', 'error');
            return false;
        }

        const index = this.customers.findIndex(c => c.id === customerId);
        if (index === -1) return false;

        this.customers.splice(index, 1);
        this.saveCustomers();
        this.loadCustomersTable();

        return true;
    }

    /**
     * Load and display customers table
     */
    loadCustomersTable() {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.customers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">No customers found</td>
                </tr>
            `;
            return;
        }

        this.customers.forEach(customer => {
            const invoiceCount = this.invoices.filter(inv => inv.customerId === customer.id).length;
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${customer.email}</td>
                <td>${customer.phone}</td>
                <td>${customer.address}</td>
                <td>${invoiceCount}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="invoicesManager.editCustomer(${customer.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="invoicesManager.deleteCustomerConfirm(${customer.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * Handle create customer form submission
     */
    handleCreateCustomer(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const customerData = Object.fromEntries(formData);

        // Validation
        if (!customerData.name || !customerData.email) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        // Check if email already exists
        if (this.customers.some(c => c.email === customerData.email)) {
            this.showAlert('Customer with this email already exists.', 'error');
            return;
        }

        const newCustomer = this.createCustomer(customerData);
        this.showAlert(`Customer ${newCustomer.name} created successfully!`, 'success');

        // Reset form and close modal
        event.target.reset();
        this.hideModal('createCustomerModal');
    }

    /**
     * Edit customer
     */
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        // Populate edit form
        this.populateCustomerEditForm(customer);
        this.showModal('editCustomerModal');
    }

    /**
     * Populate customer edit form
     */
    populateCustomerEditForm(customer) {
        document.getElementById('editCustomerId').value = customer.id;
        document.getElementById('editCustomerName').value = customer.name;
        document.getElementById('editCustomerEmail').value = customer.email;
        document.getElementById('editCustomerPhone').value = customer.phone || '';
        document.getElementById('editCustomerAddress').value = customer.address || '';
    }

    /**
     * Handle edit customer form submission
     */
    handleEditCustomer(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const customerData = Object.fromEntries(formData);

        const customerId = parseInt(customerData.customerId);

        // Validation
        if (!customerData.name || !customerData.email) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        // Check if email already exists (excluding current customer)
        if (this.customers.some(c => c.email === customerData.email && c.id !== customerId)) {
            this.showAlert('Customer with this email already exists.', 'error');
            return;
        }

        // Update customer
        const updateData = {
            name: customerData.name,
            email: customerData.email,
            phone: customerData.phone || '',
            address: customerData.address || ''
        };

        const updatedCustomer = this.updateCustomer(customerId, updateData);
        if (updatedCustomer) {
            this.showAlert(`Customer ${updatedCustomer.name} updated successfully!`, 'success');
            this.hideModal('editCustomerModal');
            // Update invoice table if customer name changed
            this.loadInvoicesTable();
        } else {
            this.showAlert('Failed to update customer.', 'error');
        }
    }

    /**
     * Delete customer with confirmation
     */
    deleteCustomerConfirm(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        if (confirm(`Are you sure you want to delete customer ${customer.name}? This action cannot be undone.`)) {
            if (this.deleteCustomer(customerId)) {
                this.showAlert('Customer deleted successfully!', 'success');
            }
        }
    }

    /**
     * Notify dashboard of changes
     */
    notifyDashboard() {
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboardMetrics();
        }
    }
}

// Create global instance
window.invoicesManager = new InvoicesManager();

// Global function for form submission (backup)
window.handleCreateInvoice = function(event) {
    if (window.invoicesManager) {
        window.invoicesManager.handleCreateInvoice(event);
    }
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.invoicesManager.init();
    });
} else {
    window.invoicesManager.init();
}
