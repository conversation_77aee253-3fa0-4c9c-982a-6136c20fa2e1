# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=enterprise_accounting

# JWT Configuration (Change in production!)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars

# Encryption Configuration (Change in production!)
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Application Configuration
NODE_ENV=development
PORT=3000

# Security Configuration
ALLOWED_ORIGINS=http://localhost:3000
SESSION_SECRET=your-session-secret-key

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# Backup Configuration
BACKUP_PATH=./backups
BACKUP_RETENTION_DAYS=30
